'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { useSlideStore } from '@/hooks/useSlideStore'

interface SlideViewProps {
  isSelected: boolean
  onSelectionChange: (selected: boolean) => void
}

const SlideView: React.FC<SlideViewProps> = ({ 
  isSelected, 
  onSelectionChange 
}) => {
  const { pages, activePage, getCurrentPage, handleCellClick } = useSlideStore()
  const [selectedGrid, setSelectedGrid] = useState<{ row: number; col: number } | null>(null)

  const currentPage = getCurrentPage()

  // 页面切换时重置选中状态
  useEffect(() => {
    setSelectedGrid(null)
  }, [currentPage?.id])

  // 处理容器点击
  const handleContainerClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onSelectionChange(true)
    setSelectedGrid(null)
    return false
  }, [onSelectionChange])

  // 处理网格点击
  const handleGridClick = useCallback((e: React.MouseEvent, row: number, col: number) => {
    e.preventDefault()
    e.stopPropagation()

    // 切换选中状态
    if (selectedGrid?.row === row && selectedGrid?.col === col) {
      setSelectedGrid(null)
    } else {
      setSelectedGrid({ row, col })
      handleCellClick({ row, col, pageId: currentPage?.id || '', cellId: `${row}-${col}` })
    }
    
    onSelectionChange(true)
    return false
  }, [selectedGrid, handleCellClick, onSelectionChange, currentPage?.id])

  // 全局点击处理
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const target = e.target as Element
      const isGridClick = target?.closest('.slide-view-container')
      
      if (!isGridClick) {
        onSelectionChange(false)
        setSelectedGrid(null)
      }
    }

    document.addEventListener('click', handleGlobalClick)
    return () => document.removeEventListener('click', handleGlobalClick)
  }, [onSelectionChange])

  if (!currentPage) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
        <div className="text-center space-y-4">
          <div className="text-gray-400 text-4xl">📄</div>
          <p className="text-gray-500">没有可显示的页面</p>
        </div>
      </div>
    )
  }

  return (
    <div 
      className="slide-view-container w-full h-full bg-white rounded-lg shadow-lg overflow-hidden" 
      onClick={handleContainerClick}
    >
      {/* 页面标题栏 */}
      <div className="bg-gray-50 border-b border-gray-200 px-4 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-800">
            {currentPage.title}
          </h2>
          <div className="text-sm text-gray-500">
            ID: {currentPage.id}
          </div>
        </div>
      </div>

      {/* 主编辑区域 */}
      <div className="flex-1 flex items-center justify-center p-4 bg-gray-50">
        {/* 固定正方形容器 */}
        <div 
          className={`relative bg-white shadow-lg border flex-shrink-0 transition-all duration-200 ${
            isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
          }`}
          style={{
            width: '1150px',
            height: '1150px'
          }}
        >
          {/* 背景图片层 */}
          <img
            src="/demo.png"
            alt="Background"
            className="absolute inset-0 w-full h-full object-cover z-10"
            style={{ objectFit: 'cover' }}
          />
          
          {/* 网格覆盖层 */}
          <div className="grid-33x33 grid-container absolute inset-0 z-50">
            {Array.from({ length: 33 }, (_, row) =>
              Array.from({ length: 33 }, (_, col) => {
                const cellKey = `${row}-${col}`
                const isSelectedCell = selectedGrid?.row === row && selectedGrid?.col === col
                const clickCount = currentPage.gridData?.[cellKey]?.clickCount || 0
                
                return (
                  <div
                    key={cellKey}
                    data-grid-cell={`${row}-${col}`}
                    className="cursor-pointer transition-all duration-200 flex items-center justify-center relative"
                    style={
                      isSelectedCell
                        ? {
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            border: '2px solid rgb(59, 130, 246)',
                            boxShadow: '0 0 0 2px rgba(59, 130, 246, 0.3), 0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            borderRadius: '2px'
                          }
                        : clickCount > 0
                        ? {
                            border: '1px solid rgba(156, 163, 175, 0.4)',
                            backgroundColor: `rgba(59, 130, 246, ${Math.min(clickCount * 0.1, 0.3)})`
                          }
                        : {
                            border: '1px solid rgba(255, 255, 255, 0.2)'
                          }
                    }
                    onClick={(e) => handleGridClick(e, row, col)}
                    title={`格子 (${row}, ${col})${isSelectedCell ? ' - 已选中' : ''}${clickCount > 0 ? ` - 点击次数: ${clickCount}` : ''}`}
                  >
                    {/* 显示点击次数 */}
                    {clickCount > 0 && (
                      <span
                        className={`text-xs font-medium pointer-events-none select-none transition-colors duration-200 ${
                          isSelectedCell ? 'text-white font-semibold' : 'text-gray-800'
                        }`}
                        style={{
                          fontSize: '10px',
                          lineHeight: '1',
                          textShadow: isSelectedCell ? '0 1px 2px rgba(0,0,0,0.3)' : '0 0 2px rgba(255,255,255,0.8)'
                        }}
                      >
                        {clickCount}
                      </span>
                    )}
                  </div>
                )
              })
            )}
          </div>
          
          {/* 页面元素渲染区域 */}
          <div className="absolute inset-0 pointer-events-none z-20">
            {currentPage.elements.map(element => (
              <div
                key={element.id}
                className="absolute pointer-events-auto"
                style={{
                  left: element.x,
                  top: element.y,
                  width: element.width,
                  height: element.height,
                  transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined,
                  zIndex: element.zIndex || 1,
                  opacity: element.opacity || 1
                }}
              >
                {/* 根据元素类型渲染不同内容 */}
                {element.type === 'text' && (
                  <div
                    className="w-full h-full flex items-center justify-center"
                    style={{
                      fontSize: element.fontSize || 16,
                      color: element.color || '#000',
                      fontWeight: element.fontWeight || 'normal',
                      textAlign: element.textAlign as any || 'center'
                    }}
                  >
                    {element.content}
                  </div>
                )}
                
                {element.type === 'image' && (
                  <img
                    src={element.src}
                    alt={element.alt || ''}
                    className="w-full h-full object-cover"
                  />
                )}
                
                {element.type === 'shape' && (
                  <div
                    className="w-full h-full"
                    style={{
                      backgroundColor: element.color || '#ccc',
                      borderRadius: element.borderRadius || '0',
                      border: element.border || 'none'
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SlideView
