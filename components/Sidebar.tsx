'use client'

import { useState, useRef, useCallback } from 'react'
import { useSlideStore } from '@/hooks/useSlideStore'
import { formatTime } from '@/lib/utils'

export default function Sidebar() {
  const { 
    pages, 
    activePage, 
    setActivePage, 
    addPage, 
    deletePage, 
    updatePageTitle 
  } = useSlideStore()
  
  // 标题编辑相关状态
  const [editingIndex, setEditingIndex] = useState(-1)
  const [editingTitle, setEditingTitle] = useState('')
  const [originalTitle, setOriginalTitle] = useState('')
  const [isEnterPressed, setIsEnterPressed] = useState(false)
  
  // 输入框引用
  const inputRefs = useRef<{ [key: number]: HTMLInputElement | null }>({})

  // 处理缩略图点击选中
  const handleThumbnailClick = useCallback((index: number, e: React.MouseEvent) => {
    e.stopPropagation()
    setActivePage(index)
  }, [setActivePage])

  // 添加新页面
  const handleAddPage = useCallback(() => {
    addPage()
  }, [addPage])

  // 删除页面
  const handleDeletePage = useCallback((index: number) => {
    if (pages.length <= 1) {
      return
    }

    const confirmDelete = window.confirm(`确定要删除页面"${pages[index].title}"吗？`)
    if (!confirmDelete) return

    deletePage(index)
  }, [pages, deletePage])

  // 开始编辑标题
  const handleTitleEdit = useCallback((index: number, currentTitle: string) => {
    setEditingIndex(index)
    setEditingTitle(currentTitle || '未命名')
    setOriginalTitle(currentTitle || '未命名')
    
    setTimeout(() => {
      const input = inputRefs.current[index]
      if (input) {
        input.focus()
        input.select()
      }
    }, 0)
  }, [])

  // 标题保存逻辑
  const handleTitleSave = useCallback((index: number) => {
    const trimmedTitle = editingTitle.trim() || '未命名'

    if (trimmedTitle !== originalTitle) {
      updatePageTitle(index, trimmedTitle)
    }

    setEditingIndex(-1)
    setEditingTitle('')
    setOriginalTitle('')
  }, [editingTitle, originalTitle, updatePageTitle])

  // 处理回车键保存
  const handleEnterSave = useCallback((index: number, event: React.KeyboardEvent) => {
    event.preventDefault()
    setIsEnterPressed(true)

    handleTitleSave(index)

    setTimeout(() => {
      setIsEnterPressed(false)
    }, 100)
  }, [handleTitleSave])

  // 处理失焦事件
  const handleTitleBlur = useCallback((index: number) => {
    if (isEnterPressed) {
      return
    }

    handleTitleSave(index)
  }, [isEnterPressed, handleTitleSave])

  const handleTitleCancel = useCallback(() => {
    setEditingIndex(-1)
    setEditingTitle('')
    setOriginalTitle('')
    setIsEnterPressed(false)
  }, [])

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 添加按钮 - 居中 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <div className="flex justify-center">
          <button
            onClick={handleAddPage}
            className="w-12 h-12 bg-gray-400 hover:bg-gray-500 text-white rounded-full flex items-center justify-center transition-colors text-xl"
            title="添加新页面"
          >
            +
          </button>
        </div>
      </div>

      {/* 页面列表 */}
      <div className="flex-1 overflow-y-auto custom-scrollbar p-4">
        {pages.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-gray-500">
            暂无页面
          </div>
        ) : (
          <div className="space-y-4">
            {pages.map((page, index) => (
              <div
                key={page.id}
                className={`
                  relative group
                  p-2 rounded-lg cursor-pointer bg-white shadow-md
                  ${activePage === index 
                    ? 'border-2 border-gray-400' 
                    : 'border border-gray-300'}
                `}
                onClick={(e) => {
                  e.stopPropagation()
                  handleThumbnailClick(index, e)
                }}
              >
                {/* 缩略图容器 - 固定样式 */}
                <div className="aspect-square rounded-lg overflow-hidden relative mb-2">
                  {/* 背景图片缩略图 */}
                  <img
                    src="/demo.png"
                    alt="Page thumbnail"
                    className="absolute inset-0 w-full h-full object-cover"
                  />
                  
                  {/* 网格缩略图 */}
                  <div className="absolute inset-0 grid grid-cols-16 grid-rows-16 opacity-20">
                    {Array.from({ length: 256 }, (_, i) => (
                      <div key={i} className="border border-white border-opacity-30"></div>
                    ))}
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleTitleEdit(index, page.title)
                      }}
                      className="w-8 h-8 bg-black bg-opacity-70 text-white rounded hover:bg-opacity-90 flex items-center justify-center"
                      title="重命名"
                    >
                      ✏️
                    </button>
                    
                    {pages.length > 1 && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeletePage(index)
                        }}
                        className="w-8 h-8 bg-black bg-opacity-70 text-white rounded hover:bg-opacity-90 flex items-center justify-center"
                        title="删除页面"
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </div>
                
                {/* 页面信息 - 分层布局，编辑时不影响时间显示 */}
                <div className="px-2 py-1 min-h-[45px]">
                  {editingIndex === index ? (
                    /* 编辑模式 - 输入框和时间都显示 */
                    <div className="text-center space-y-1">
                      <input
                        ref={el => { inputRefs.current[index] = el }}
                        type="text"
                        value={editingTitle}
                        onChange={e => setEditingTitle(e.target.value)}
                        onKeyDown={e => {
                          if (e.key === 'Enter') {
                            handleEnterSave(index, e)
                          } else if (e.key === 'Escape') {
                            handleTitleCancel()
                          }
                        }}
                        onBlur={() => handleTitleBlur(index)}
                        className="w-full px-2 py-1 text-sm text-center bg-transparent focus:outline-none"
                        style={{ 
                          border: 'none',
                          boxShadow: 'none'
                        }}
                        disabled={false}
                      />
                      <div className="text-xs text-gray-400 px-1">
                        {formatTime(page.createTime)}
                      </div>
                    </div>
                  ) : (
                    /* 正常显示模式 */
                    <div className="text-center space-y-1">
                      <div
                        className="text-sm font-medium text-gray-800 truncate px-1 py-1"
                        onDoubleClick={() => handleTitleEdit(index, page.title)}
                        title={page.title}
                      >
                        {page.title}
                      </div>
                      <div className="text-xs text-gray-400 px-1 pb-1">
                        {formatTime(page.createTime)}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
} 