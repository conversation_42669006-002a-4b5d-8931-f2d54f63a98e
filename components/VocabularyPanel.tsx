'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { textApi } from '@/lib/api'
import type { TextInfo } from '@/lib/types'

// 颜色配置 - 按照图片顺序：灰、红、紫、青、黄、粉、蓝、绿、橙
// color字段对应：灰0、红1、紫2、青3、黄4、粉5、蓝6、绿7、橙8
const colorContainers = [
  { id: 'gray', color: 0, bgColor: 'bg-gray-300', dotColor: 'bg-gray-500' },
  { id: 'red', color: 1, bgColor: 'bg-red-300', dotColor: 'bg-red-500' },
  { id: 'purple', color: 2, bgColor: 'bg-purple-300', dotColor: 'bg-purple-500' },
  { id: 'cyan', color: 3, bgColor: 'bg-cyan-300', dotColor: 'bg-cyan-500' },
  { id: 'yellow', color: 4, bgColor: 'bg-yellow-300', dotColor: 'bg-yellow-500' },
  { id: 'pink', color: 5, bgColor: 'bg-pink-300', dotColor: 'bg-pink-500' },
  { id: 'blue', color: 6, bgColor: 'bg-blue-300', dotColor: 'bg-blue-500' },
  { id: 'green', color: 7, bgColor: 'bg-green-300', dotColor: 'bg-green-500' },
  { id: 'orange', color: 8, bgColor: 'bg-orange-300', dotColor: 'bg-orange-500' }
]

const VocabularyPanel: React.FC = () => {
  // 本地状态 - 保存文本内容
  const [texts, setTexts] = useState<Record<string, string>>(() => {
    const initial: Record<string, string> = {}
    colorContainers.forEach(container => {
      initial[container.id] = ''
    })
    return initial
  })
  // 保存后端返回的ID映射
  const [textIds, setTextIds] = useState<Record<string, string | null>>(() => {
    const initial: Record<string, string | null> = {}
    colorContainers.forEach(container => {
      initial[container.id] = null
    })
    return initial
  })
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editingValue, setEditingValue] = useState<string>('')
  const [originalValue, setOriginalValue] = useState<string>('')

  const [loading, setLoading] = useState<boolean>(false)




  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // 页面加载时获取文本数据
  useEffect(() => {
    loadTextData()
  }, [])



  // 加载文本数据
  const loadTextData = async () => {
    try {
      setLoading(true)
      const textInfoList = await textApi.find()

      // 将后端数据按color字段映射到对应的颜色容器
      const newTexts: Record<string, string> = {}
      const newTextIds: Record<string, string | null> = {}

      // 初始化所有容器
      colorContainers.forEach(container => {
        newTexts[container.id] = ''
        newTextIds[container.id] = null
      })

      // 映射后端数据
      textInfoList.forEach((textInfo: TextInfo) => {
        const container = colorContainers.find(c => c.color === textInfo.color)
        if (container) {
          newTexts[container.id] = textInfo.text
          newTextIds[container.id] = textInfo.id  // 保存后端ID
        }
      })

      setTexts(newTexts)
      setTextIds(newTextIds)

      console.log('📊 加载文本数据完成:', textInfoList.length, '条记录')
    } catch (error) {
      console.error('加载文本数据失败:', error)
    } finally {
      setLoading(false)
    }
  }





  // 开始编辑
  const handleStartEdit = (id: string) => {
    setEditingId(id)
    setEditingValue(texts[id] || '')
    setOriginalValue(texts[id] || '')
  }

  // 处理文本变化
  const handleTextChange = (value: string) => {
    setEditingValue(value)
  }

  // 结束编辑
  const handleEndEdit = useCallback(async (save: boolean = true) => {
    if (!editingId) return

    if (save) {
      try {
        // 检查文本是否发生变化
        const hasTextChanged = editingValue !== originalValue

        console.log('💾 保存文本:', hasTextChanged ? '有变化' : '无变化')

        if (hasTextChanged) {
          // 只有文本发生变化时才调用后端API

          const container = colorContainers.find(c => c.id === editingId)
          if (container) {
            // 获取对应的后端ID
            const backendId = textIds[editingId]

            // 调用后端API保存
            if (backendId) {
              console.log('📤 更新文本:', container.color, '→', editingValue.substring(0, 20) + '...')

              const response = await textApi.update({
                id: String(backendId), // 确保ID作为字符串传输
                color: container.color,
                text: editingValue
              })

              console.log('✅ text/update响应:', response)

              // 更新本地状态为后端返回的数据
              setTexts(prev => ({ ...prev, [editingId]: response.text }))
              setTextIds(prev => ({ ...prev, [editingId]: response.id }))
            } else {
              console.warn('没有后端ID，无法更新文本')
            }
          }
        } else {
          console.log('文本未发生变化，跳过后端更新')
        }

        // 如果没有调用API（文本未变化），更新本地状态
        if (!hasTextChanged) {
          setTexts(prev => ({ ...prev, [editingId]: editingValue }))
        }
      } catch (error) {
        console.error('保存文本失败:', error)
        // 保存失败时恢复原值
        setEditingValue(originalValue)
      }
    } else {
      // 取消编辑，恢复原值
      setEditingValue(originalValue)
    }

    setEditingId(null)
    setEditingValue('')
    setOriginalValue('')
  }, [editingId, editingValue, originalValue, textIds])

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleEndEdit(true)
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleEndEdit(false)
    }
  }



  // 处理双击编辑
  const handleDoubleClick = (containerId: string, event: React.MouseEvent) => {
    // 先计算光标位置（基于当前容器的文本）
    const currentText = texts[containerId] || ''
    const containerElement = event.currentTarget as HTMLElement
    const rect = containerElement.getBoundingClientRect()

    // 计算点击位置相对于容器的坐标
    const x = event.clientX - rect.left - 16 // 减去padding
    const y = event.clientY - rect.top - 16  // 减去padding

    // 开始编辑
    handleStartEdit(containerId)
  }



  return (
    <div className="h-full flex flex-col bg-white">
      {/* 标题栏 */}
      <div className="bg-gray-50 border-b border-gray-200 px-4 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">词汇面板</h3>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {loading && (
          <div className="text-center py-4">
            <div className="text-gray-500">加载中...</div>
          </div>
        )}
        {colorContainers.map((container) => {
          const isEditing = editingId === container.id
          const text = texts[container.id] || ''

          return (
            <div key={container.id} className="space-y-2">
              <div className="flex items-start space-x-3">
                {/* 颜色点 */}
                <div
                  className={`${container.dotColor} w-4 h-4 rounded-full flex-shrink-0`}
                />

                {/* 容器 */}
                <div
                  className={`${container.bgColor} rounded-lg flex-1 cursor-pointer transition-all duration-300 hover:shadow-md min-h-[160px] p-4`}
                  data-container-id={container.id}
                  onDoubleClick={(e) => handleDoubleClick(container.id, e)}
                >
                  {isEditing ? (
                    <textarea
                      ref={textareaRef}
                      value={editingValue}
                      onChange={(e) => handleTextChange(e.target.value)}
                      onBlur={() => handleEndEdit(true)}
                      onKeyDown={handleKeyDown}
                      className="w-full h-full bg-transparent border-none outline-none resize-none text-gray-800"
                      style={{ minHeight: '48px' }}
                      onDoubleClick={(e) => {
                        e.stopPropagation()
                      }}
                        />
                  ) : (
                    <div className="w-full h-full text-gray-800 whitespace-pre-wrap break-words">
                      {text}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default VocabularyPanel
