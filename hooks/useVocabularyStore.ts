import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { generateId } from '@/lib/utils'
import { COLOR_CLASSES } from '@/lib/types'
import type { VocabularyModule, TextItem } from '@/lib/types'

interface VocabularyState {
  // 状态数据
  modules: VocabularyModule[]

  // 词汇操作方法
  addTextItem: (moduleIndex: number, content: string) => void
  updateTextItem: (moduleIndex: number, itemIndex: number, content: string) => void
  deleteTextItem: (moduleIndex: number, itemIndex: number) => void
  reorderTextItems: (moduleIndex: number, newItems: TextItem[]) => void
  toggleModule: (moduleIndex: number) => void
  startEditing: (moduleIndex: number, itemIndex: number) => void
  stopEditing: (moduleIndex: number, itemIndex: number, save?: boolean) => void
  selectTextItem: (moduleIndex: number, itemIndex: number, selected: boolean) => void

  // 辅助方法
  initializeModules: () => void
  generateNewTextItem: (content?: string) => TextItem
  findTextItemById: (id: string) => { moduleIndex: number; itemIndex: number } | null
}

export const useVocabularyStore = create<VocabularyState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      modules: COLOR_CLASSES.map((colorClass, index) => ({
        id: `module-${index}`,
        colorClass,
        expanded: true,
        textItems: []
      })),

      // 初始化词汇模块
      initializeModules: () => {
        const modules: VocabularyModule[] = COLOR_CLASSES.map((colorClass, index) => ({
          id: `module-${index}`,
          colorClass,
          expanded: true,
          textItems: []
        }))

        set({ modules })
      },

      // 添加文本项
      addTextItem: (moduleIndex: number, content: string) => {
        const newTextItem = get().generateNewTextItem(content)

        set(state => {
          const newModules = [...state.modules]
          newModules[moduleIndex].textItems.push(newTextItem)
          return { modules: newModules }
        })
      },

      // 生成新文本项
      generateNewTextItem: (content = '') => {
        return {
          id: generateId(),
          phraseId: null,
          content,
          editing: false,
          originalContent: content,
          selected: false,
          wordReplacing: false,
          dragging: false
        }
      },

      // 切换模块展开/收起
      toggleModule: (moduleIndex: number) => {
        set(state => {
          const newModules = [...state.modules]
          newModules[moduleIndex].expanded = !newModules[moduleIndex].expanded
          return { modules: newModules }
        })
      },

      // 更新文本项
      updateTextItem: (moduleIndex: number, itemIndex: number, content: string) => {
        set(state => {
          const newModules = [...state.modules]
          if (newModules[moduleIndex]?.textItems[itemIndex]) {
            newModules[moduleIndex].textItems[itemIndex].content = content
          }
          return { modules: newModules }
        })
      },

      deleteTextItem: (moduleIndex: number, itemIndex: number) => {
        set(state => {
          const newModules = [...state.modules]
          newModules[moduleIndex].textItems.splice(itemIndex, 1)
          return { modules: newModules }
        })
      },

      reorderTextItems: (moduleIndex: number, newItems: TextItem[]) => {
        set(state => {
          const newModules = [...state.modules]
          newModules[moduleIndex].textItems = newItems
          return { modules: newModules }
        })
      },

      startEditing: (moduleIndex: number, itemIndex: number) => {
        set(state => {
          const newModules = [...state.modules]
          const textItem = newModules[moduleIndex].textItems[itemIndex]
          if (textItem) {
            textItem.editing = true
            textItem.originalContent = textItem.content
          }
          return { modules: newModules }
        })
      },

      stopEditing: (moduleIndex: number, itemIndex: number, save = true) => {
        set(state => {
          const newModules = [...state.modules]
          const textItem = newModules[moduleIndex]?.textItems[itemIndex]

          if (textItem) {
            if (!save) {
              // 取消编辑，恢复原内容
              textItem.content = textItem.originalContent
            }
            textItem.editing = false
          }

          return { modules: newModules }
        })
      },

      selectTextItem: (moduleIndex: number, itemIndex: number, selected: boolean) => {
        set(state => {
          const newModules = [...state.modules]
          const textItem = newModules[moduleIndex]?.textItems[itemIndex]
          if (textItem) {
            textItem.selected = selected
          }
          return { modules: newModules }
        })
      },

      findTextItemById: (id: string) => {
        const { modules } = get()
        for (let moduleIndex = 0; moduleIndex < modules.length; moduleIndex++) {
          const itemIndex = modules[moduleIndex].textItems.findIndex(item => item.id === id)
          if (itemIndex !== -1) {
            return { moduleIndex, itemIndex }
          }
        }
        return null
      }
    }),
    {
      name: 'vocabulary-store'
    }
  )
) 