import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { generateId } from '@/lib/utils'
import type { Page, CellInfo } from '@/lib/types'

interface SlideState {
  // 状态数据
  pages: Page[]
  activePage: number

  // 页面操作方法
  addPage: () => void
  deletePage: (index: number) => void
  updatePageTitle: (index: number, title: string) => void
  reorderPages: (newPages: Page[]) => void
  setActivePage: (index: number) => void
  handleCellClick: (cellInfo: CellInfo) => void
  duplicatePage: () => void

  // 辅助方法
  generateNewPage: (title?: string) => Page
  getCurrentPage: () => Page | null
}

export const useSlideStore = create<SlideState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      pages: [
        {
          id: 'initial-page-1',
          title: '页面 1',
          elements: [],
          createTime: '2024-01-01T00:00:00.000Z',
          gridData: {}
        }
      ],
      activePage: 0,

      // 生成新页面
      generateNewPage: (title = '未命名') => {
        return {
          id: generateId(),
          title,
          elements: [],
          createTime: new Date().toISOString(),
          gridData: {}
        }
      },

      // 获取当前活动页面
      getCurrentPage: () => {
        const { pages, activePage } = get()
        return pages[activePage] || null
      },

      // 添加新页面
      addPage: () => {
        const newPage = get().generateNewPage(`页面 ${get().pages.length + 1}`)

        set(state => ({
          pages: [...state.pages, newPage],
          activePage: state.pages.length
        }))
      },

      // 删除页面
      deletePage: (index) => {
        const { pages } = get()

        if (pages.length <= 1) {
          return
        }

        set(state => {
          const newPages = [...state.pages]
          newPages.splice(index, 1)

          // 调整激活页索引
          let newActivePage = state.activePage
          if (newActivePage >= newPages.length) {
            newActivePage = Math.max(0, newPages.length - 1)
          } else if (newActivePage > index) {
            newActivePage = newActivePage - 1
          }

          return {
            pages: newPages,
            activePage: newActivePage
          }
        })
      },

      // 更新页面标题
      updatePageTitle: (index, title) => {
        set(state => {
          const newPages = [...state.pages]
          if (newPages[index]) {
            newPages[index] = { ...newPages[index], title }
          }
          return { pages: newPages }
        })
      },

      // 页面重排序
      reorderPages: (newPages) => {
        set({ pages: newPages })
        
        // 确保激活页索引在有效范围内
        const { activePage } = get()
        if (activePage >= newPages.length) {
          set({ activePage: Math.max(0, newPages.length - 1) })
        }
      },

      // 设置激活页面
      setActivePage: (index) => {
        const { pages } = get()
        if (index >= 0 && index < pages.length) {
          set({ activePage: index })
        }
      },

      // 处理格子点击事件
      handleCellClick: (cellInfo) => {
        set(state => {
          const { pages, activePage } = state
          const currentPage = pages[activePage]

          if (!currentPage) return state

          const cellKey = `${cellInfo.row}-${cellInfo.col}`
          const newGridData = { ...currentPage.gridData }

          if (!newGridData[cellKey]) {
            newGridData[cellKey] = { clickCount: 0 }
          }
          newGridData[cellKey].clickCount++

          const newPages = [...pages]
          newPages[activePage] = {
            ...currentPage,
            gridData: newGridData
          }

          return { pages: newPages }
        })
      },

      // 复制页面
      duplicatePage: () => {
        const { pages, activePage } = get()
        const currentPage = pages[activePage]
        
        if (currentPage) {
          const duplicatedPage = {
            ...currentPage,
            id: generateId(),
            title: `${currentPage.title} - 副本`,
            createTime: new Date().toISOString()
          }
          
          set(state => ({
            pages: [...state.pages, duplicatedPage],
            activePage: state.pages.length
          }))
        }
      }
    }),
    {
      name: 'slide-store'
    }
  )
) 