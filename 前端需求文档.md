# Cube Web 前端开发需求文档

## 📋 文档信息
- **项目名称**: Cube Web - 智能幻灯片编辑器
- **文档版本**: v2.0.0
- **创建日期**: 2025-07-23
- **目标开发者**: 前端开发团队
- **技术栈**: Next.js 14 + React 18 + TypeScript + Tailwind CSS + Zustand

---

## 🎯 项目概述

Cube Web 是一个现代化的智能编辑器，具有独特的33x33网格交互系统和9色词汇管理功能。用户可以通过网格点击、词汇编辑、页面管理等方式创建和编辑内容。

### 核心特性
- 33x33网格交互系统，支持精确位置控制和坐标数据绑定
- 9色分类词汇管理，支持实时编辑、顿号检测和自动保存
- 多页面管理，支持创建、删除、重命名，与后端表格数据同步
- 智能光标定位和文本编辑体验
- 网格选词功能，支持键盘操作和词汇切换
- 实时词汇监控，自动检测顿号增删和词汇编辑

---

## 🏗️ 1. 页面与功能结构概览

### 1.1 页面路径结构

```
/                           # 主页面 (唯一页面)
├── 左侧边栏                 # 页面管理区域
├── 中央主视图               # 幻灯片编辑区域  
└── 右侧词汇面板             # 词汇管理区域
```

### 1.2 功能模块详细说明

#### 1.2.1 主页面 (`/`)
**页面路径**: `/`  
**页面用途**: 幻灯片编辑器主界面  
**页面用途描述**: 

- 提供完整的幻灯片编辑功能
- 集成页面管理、网格交互、词汇管理三大核心功能
- 采用三栏布局设计，左侧边栏+ 中央主视图+ 右侧词汇面板

#### 1.2.2 左侧边栏 - 页面管理模块
**功能用途**: 页面列表管理和导航  
**核心功能**:

- 页面创建：点击"+"按钮创建新页面
- 页面切换：点击不同缩略图切换到对应网格页面
- 页面重命名：双击标题或点击编辑按钮进行重命名
- 页面删除：点击删除按钮删除页面（需确认）

#### 1.2.3 中央主视图 - 幻灯片编辑模块
**功能用途**: 幻灯片内容编辑和网格交互  
**核心功能**:

- 33x33网格系统
- 背景图片显示：固定背景图片`/demo.png`
- 网格选中状态：高亮显示选中单元格
- 网格选词功能：单击网格进入选词模式，键盘左键/右键操作切换词汇，
- 页面元素渲染：支持文本、图片、形状等元素显示

#### 1.2.4 右侧词汇面板 - 词汇管理模块
**功能用途**: 9色分类词汇管理和编辑  
**核心功能**:
- 9色容器系统：灰、红、紫、青、黄、粉、蓝、绿、橙
- 双击编辑：智能光标定位，支持多行文本编辑
- 实时保存：文本变化检测，自动调用API保存
- 容器折叠：支持容器展开/折叠操作
- 数据加载：页面加载时自动获取词汇数据
- 实时监控：根据用户是否输入“顿号”，或者删除“顿号”，是否正在修改词库

---

## 🔌 2. 接口定义 (API文档)

### 2.1 API基础配置
- **API基础地址**: `http://localhost:8080`
- **请求头配置**:
  ```json
  {
    "Content-Type": "application/json",
    "Accept": "application/json"
  }
  ```

### 2.2 文本信息管理 API

#### 2.2.1 获取所有文本信息
```typescript
GET /text/find

// 响应格式
TextInfo[]

interface TextInfo {
  id: number;        // 文本ID
  color: number;     // 颜色索引 (0-8)
  text: string;      // 文本内容
}
```

#### 2.2.2 更新文本信息
```typescript
PUT /text/update

// 请求参数
{
  id?: number;       // 可选，保留字段
  color: number;     // 0-8，颜色索引，后端通过此字段定位记录
  text: string;      // 文本内容
}

// 响应格式
{
  message: string;   // 操作结果消息
}
```

### 2.3 表格管理 API

#### 2.3.1 创建表格
```typescript
POST /table/add

// 请求参数
{
  name: string;      // 表格名称 (1-255字符)
}

// 响应格式
{
  id: number;
  name: string;
  create_time: string;
}
```

#### 2.3.2 获取表格列表
```typescript
GET /table/page

// 响应格式
{
  tables: Table[];
  total: number;
}

interface Table {
  id: number;
  name: string;
  create_time: string;  // ISO 8601格式
}
```

#### 2.3.3 更新表格
```typescript
PUT /table/update

// 请求参数
{
  id: number;
  name: string;
}

// 响应格式
{
  message: string;
}
```

#### 2.3.4 删除表格
```typescript
DELETE /table/delete?id={id}

// 查询参数
id: number

// 响应格式
{
  message: string;
}
```

### 2.4 词汇管理 API

#### 2.4.1 添加词汇
```typescript
POST /phrase/add

// 请求参数
{
  color: number;     // 0-8
  new_text: string;  // 新的文本内容
}

// 响应格式
{
  message: string;   // "添加成功" 或 "没有新增词汇"
  text_info: {       // 更新后的完整文本信息对象
    id: number;
    color: number;
    text: string;
  };
}
```

#### 2.4.2 删除词汇
```typescript
DELETE /phrase/delete

// 请求参数
{
  color: number;     // 0-8
  text: string;
}

// 响应格式
{
  message: string;        // "删除成功" 或 "没有需要删除的词汇"
  textInfoList: TextInfo[]; // 所有被影响的文本信息列表
}
```

#### 2.4.3 更新词汇 (新增)
```typescript
PUT /phrase/update

// 请求参数
{
  color: number;     // 0-8
  text: string;      // 当前编辑的文本内容
}

// 响应格式
{
  message: string;        // "更新成功"
  textInfoList: TextInfo[]; // 所有被影响的文本信息列表
}
```

#### 2.4.4 根据颜色查询词汇列表
```typescript
GET /phrase/list?color={color}

// 查询参数
color: number

// 响应格式
{
  phrases: Phrase[];
  total: number;
}

interface Phrase {
  id: number;
  text_id: number;
  word: string;
  type: number;      // 重复次数标识
}
```

### 2.5 坐标管理 API

#### 2.5.1 查询坐标关联的词汇列表 (修改)
```typescript
GET /coordinate/list?color={color}

// 查询参数
color: number  // 只需要颜色参数

// 响应格式
{
  phrases: Phrase[];
  total: number;
}
```

#### 2.5.2 更新坐标信息
```typescript
PUT /coordinate/update

// 请求参数 - 传递完整的coordinate对象
{
  id: number;
  table_id: number;
  color: number;
  position: string;
  voc?: string;
  repeated?: number;
}

// 响应格式
{
  word: string;       // 返回更新后的词汇内容
  message: string;    // 操作结果消息
}

interface Coordinate {
  id: number;
  table_id: number;
  color: number;
  position: string;   // 格式："（x， y）"
  voc: string | null; // 词汇内容，可为空
  repeated: number;   // 重复次数，默认0
}
```

#### 2.5.3 批量导入坐标数据
```typescript
GET /coordinate/batch?id={id}

// 查询参数
id: number  // 表格ID

// 响应格式
{
  coordinates: Coordinate[];
  total: number;
}
```

#### 2.5.4 查询表格的坐标数据
```typescript
GET /coordinate/find?id={id}

// 查询参数
id: number  // 表格ID

// 响应格式
{
  coordinates: Coordinate[];
  total: number;
}
```

#### 2.5.5 删除表格的所有坐标数据
```typescript
DELETE /coordinate/delete?id={id}

// 查询参数
id: number  // 表格ID

// 响应格式
{
  message: string;
}
```

---

## 🔄 3. 业务逻辑与前端行为说明

### 3.1 应用初始化流程

#### 3.1.1 页面加载流程
1. **应用启动**: 加载主页面组件 `app/page.tsx`
2. **状态初始化**:
   - 初始化页面状态
   - 初始化词汇状态
   - 根据页面初始化网格坐标状态
3. **数据加载**:
   - **调用 `/table/page` 接口获取表格列表**
   - **根据返回的表格数据创建各个页面并绑定ID**
   - 调用 `textApi.find()` 获取所有文本数据
   - 按 color 字段映射到对应的9色容器
   - 保存后端ID映射关系
4. **界面渲染**: 渲染三栏布局界面

#### 3.1.2 默认状态设置
- **根据后端表格数据创建页面列表，不再使用默认页面**
- 默认激活第一个页面 (activePage: 0)
- 9个词汇模块默认全部展开
- 网格无选中状态

### 3.2 页面管理业务逻辑

#### 3.2.1 页面创建流程
1. **触发条件**: 点击侧边栏"+"按钮
2. **执行逻辑**:
   - 调用后端API创建表格，传递页面名称
   - 根据返回的表格数据创建页面对象，使用后端返回的ID
   - 将新页面添加到页面列表并设为活动页面
3. **界面更新**: 侧边栏显示新页面缩略图，主视图切换到新页面

#### 3.2.2 页面切换流程
1. **触发条件**: 点击侧边栏页面缩略图
2. **执行逻辑**:
   - 切换到指定页面索引
   - 调用API获取当前页面的坐标数据
   - 将坐标数据绑定到网格系统
3. **界面更新**:
   - 侧边栏高亮选中页面
   - 主视图显示对应页面内容和坐标数据
   - 重置网格选中状态

#### 3.2.3 页面重命名流程
1. **触发条件**: 双击页面标题或点击编辑按钮
2. **执行逻辑**:
   - 进入编辑模式，设置编辑状态
   - 聚焦并选中输入框文本
3. **保存逻辑**:
   - Enter键或失焦时保存
   - Escape键取消编辑
   - 调用后端API更新表格名称
   - 更新本地页面标题状态

#### 3.2.4 页面删除流程
1. **触发条件**: 点击删除按钮
2. **确认机制**: 弹出确认对话框
3. **执行逻辑**:
   - 调用后端API删除对应的表格数据
   - 从页面列表中移除该页面
   - 调整活动页面索引，确保索引有效

### 3.3 网格交互业务逻辑

#### 3.3.1 网格点击流程
1. **触发条件**: 点击网格单元格
2. **执行逻辑**:
   - 生成网格单元格的唯一标识
   - 更新该单元格的点击次数数据
   - 记录网格状态变化
3. **界面更新**:
   - 显示点击次数
   - 蓝色高亮选中单元格
   - 设置选中状态

#### 3.3.2 网格选词流程
1. **触发条件**: 单击网格单元格进入选词模式
2. **获取词汇列表**:
   - 调用API获取词汇列表，只传递颜色参数
   - 显示可选词汇列表供用户选择
3. **键盘操作**:
   - 左箭头键: 切换到上一个词汇选项
   - 右箭头键: 切换到下一个词汇选项
   - Enter键: 确认选择
   - Escape键: 退出选词模式
4. **数据更新**:
   - 传递完整的坐标对象给后端更新
   - 接收返回的词汇数据更新网格显示

### 3.4 词汇管理业务逻辑

#### 3.4.1 词汇数据加载流程
1. **触发时机**: 页面加载时
2. **执行逻辑**:
   - 调用API获取所有文本数据
   - 按color字段将文本数据映射到对应的9色容器
   - 保存文本内容和后端ID的映射关系

#### 3.4.2 词汇编辑流程 (重构)
1. **触发条件**: 双击词汇容器
2. **智能光标定位**:
   - 计算点击位置对应的光标位置
   - 设置目标光标位置用于后续定位
3. **进入编辑模式**:
   - 设置当前编辑的容器ID
   - 保存当前文本内容和原始内容
   - 启动实时监控系统

#### 3.4.3 实时词汇检测系统 (新增)

##### 3.4.3.1 顿号新增检测
1. **触发条件**: 用户在编辑过程中输入顿号"、"
2. **检测逻辑**:
   - 实时监控文本变化事件
   - 统计原文本和新文本中的顿号数量
   - 当新文本顿号数量大于原文本时触发新增检测
3. **API调用与智能更新**:
   - 立即调用phrase/add接口，传递颜色和原始文本内容
   - 接收返回的text_info对象（包含处理后的文本）
   - 使用智能差异检测算法分析文本变化
   - 只更新变化的部分，光标自动保持在合适位置
   - 用户感觉像是智能补全，无跳跃感

##### 3.4.3.2 顿号删除检测
1. **触发条件**: 用户在编辑过程中删除顿号"、"
2. **检测逻辑**:
   - 实时监控文本变化事件
   - 统计原文本和新文本中的顿号数量
   - 当新文本顿号数量小于原文本时触发删除检测
3. **API调用**:
   - 立即调用phrase/delete接口，传递颜色和文本内容
   - 接收返回的受影响文本信息列表
   - 更新所有被影响的文本容器显示

##### 3.4.3.3 词汇编辑检测 (最后顿号前编辑)
1. **触发条件**: 用户编辑位置在文本内容最后一个顿号前面
2. **检测逻辑**:
   - 判断光标位置是否在最后一个顿号前面
   - 使用防抖处理，用户停止编辑0.3秒后触发
   - 避免频繁的API调用
3. **API调用**:
   - 调用phrase/update接口，传递颜色和文本内容
   - 接收返回的受影响文本信息列表
   - 更新所有被影响的文本容器显示
4. **实时监控**:
   - 监控文本变化和光标位置
   - 检查是否在最后顿号前编辑
   - 启动防抖更新机制

#### 3.4.4 容器折叠/展开流程
1. **触发条件**: 点击颜色圆点
2. **执行逻辑**:
   - 切换容器的折叠状态
   - 更新折叠容器集合
   - 根据状态决定展开或折叠
3. **界面更新**: 容器高度在16px(折叠)和160px(展开)之间切换

### 3.5 状态管理逻辑

#### 3.5.1 页面状态管理 (useSlideStore)
**主要状态**:
- 页面列表数组，存储所有页面信息
- 当前活动页面索引

**主要操作方法**:
- 添加页面、删除页面、更新页面标题
- 设置活动页面、处理网格单元格点击

#### 3.5.2 词汇状态管理 (useVocabularyStore)
**主要状态**:
- 9个词汇模块数组，对应9种颜色

**主要操作方法**:
- 添加文本项、更新文本项、删除文本项
- 切换模块展开状态、开始/停止编辑

### 3.6 错误处理机制

#### 3.6.1 API请求错误处理
- 使用try-catch包装所有API调用
- 记录错误信息到控制台
- 向用户显示友好的错误提示
- 在必要时进行数据回滚

#### 3.6.2 用户操作错误处理
- 页面删除: 最后一个页面不允许删除
- 文本编辑: 保存失败时恢复原始值
- 网格操作: 无效操作时忽略并提示

### 3.7 性能优化策略

#### 3.7.1 状态更新优化
- 使用 `useCallback` 缓存事件处理函数
- 避免不必要的组件重渲染
- 合理使用 `useMemo` 缓存计算结果

#### 3.7.2 API调用优化
- 文本变化检测，避免无效API调用
- 请求防抖处理
- 错误重试机制

---

## � 4. 新功能开发重点

### 4.1 词汇面板实时检测功能

#### 4.1.1 技术实现要点
- **实时监控**: 使用 `onChange` 事件监听文本变化
- **顿号检测**: 通过正则表达式 `/、/g` 统计顿号数量变化
- **防抖处理**: 使用 `debounce` 函数，0.3秒延迟触发API调用
- **光标位置检测**: 使用 `selectionStart` 获取光标位置，判断是否在最后顿号前

#### 4.1.2 状态管理
**新增状态变量**:
- 实时监控开关状态
- 上次顿号数量记录
- 编辑光标位置记录

**防抖函数**:
- 使用useCallback和debounce实现0.3秒延迟
- 避免频繁API调用，提升性能

### 4.2 导航栏后端集成功能

#### 4.2.1 页面数据结构调整
**Page接口修改**:
- id字段改为number类型，对应后端table.id
- title字段对应后端table.name
- createTime字段对应后端table.create_time
- 新增coordinates字段，绑定坐标数据

#### 4.2.2 API集成流程
1. **初始化**: 调用 `/table/page` 获取所有表格，创建页面列表
2. **创建页面**: 调用 `/table/add` 创建表格，返回数据创建页面
3. **删除页面**: 调用 `/table/delete` 删除表格数据
4. **重命名页面**: 调用 `/table/update` 更新表格名称

### 4.3 网格系统坐标绑定功能

#### 4.3.1 坐标数据管理
**GridCellData接口扩展**:
- 新增coordinate字段，绑定坐标对象
- 新增hasWord字段，标识是否有词汇
- 新增wordContent字段，存储词汇内容

#### 4.3.2 选词模式优化
- **简化API参数**: `/coordinate/list` 只需要color参数
- **键盘操作**: 左右箭头键切换词汇，Enter确认，Escape退出
- **数据更新**: 传递完整coordinate对象给后端，接收word数据更新界面

### 4.4 开发优先级

#### 高优先级 (P0)
1. **词汇面板顿号检测**: 实时检测顿号增删，立即调用API
2. **导航栏表格同步**: 页面CRUD操作与后端表格数据同步
3. **网格坐标绑定**: 页面切换时加载坐标数据

#### 中优先级 (P1)
1. **词汇编辑检测**: 最后顿号前编辑的防抖处理
2. **选词模式优化**: 键盘操作和API参数简化
3. **错误处理**: API调用失败的回滚机制

#### 低优先级 (P2)
1. **性能优化**: 防抖函数优化、状态更新优化
2. **用户体验**: 加载状态、操作反馈
3. **测试覆盖**: 单元测试和集成测试

---

## �📝 开发注意事项

### 3.8.1 代码规范
- 严格遵循 TypeScript 类型定义
- 使用 ESLint 和 Prettier 保持代码风格一致
- 组件命名采用 PascalCase，函数命名采用 camelCase

### 3.8.2 样式规范
- 优先使用 Tailwind CSS 原子类
- 自定义样式放在 `globals.css` 中
- 响应式设计考虑不同屏幕尺寸

### 3.8.3 测试要求
- 关键业务逻辑需要单元测试
- API 集成需要集成测试
- 用户交互需要 E2E 测试

---

## 📞 技术支持

如有开发过程中的技术问题，请联系项目技术负责人。

**文档版本**: v2.0.0
**最后更新**: 2025-07-23
**维护团队**: 前端开发组
