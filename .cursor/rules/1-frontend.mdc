---
description: 
globs: 
alwaysApply: false
---
# 前端开发规范

**更新时间**: 2025年6月27日 17:34
**更新规则**：不更改结构，只有【1.技术栈】和【2.架构】，只更新内部内容

---

## 1. 技术栈

项目基于以下技术栈构建，已完成现代化重构：

-   **框架**: [Next.js](mdc:https:/nextjs.org) (v14.2.23, App Router)
-   **语言**: [TypeScript](mdc:https:/www.typescriptlang.org) (v5, 严格模式)
-   **UI 库**: [React](mdc:https:/react.dev) (v18)
-   **样式**: [Tailwind CSS](mdc:https:/tailwindcss.com) (v3.4.1)
-   **UI 工具**: [Radix UI](mdc:https:/www.radix-ui.com), [Lucide React](mdc:https:/lucide.dev/guide/react) (v0.438.0)
-   **状态管理**: [Zustand](mdc:https:/github.com/pmndrs/zustand) (v5.0.5)
-   **工具函数**: [clsx](mdc:https:/github.com/lukeed/clsx) (v2.1.1), [tailwind-merge](mdc:https:/github.com/dcastil/tailwind-merge) (v2.5.2)
-   **代码质量**: [ESLint](mdc:https:/eslint.org), [Prettier](mdc:https:/prettier.io) (v3.3.3)
-   **测试框架**: [Jest](mdc:https:/jestjs.io) (v29.7.0), [ts-jest](mdc:https:/github.com/kulshekhar/ts-jest) (v29.4.0)
-   **构建工具**: [PostCSS](mdc:https:/postcss.org) (v8), [Tailwind CSS Animate](mdc:https:/github.com/jamiebuilds/tailwindcss-animate) (v1.0.7)

---

## 2. 架构

### 2.1 结构现状
```
app/                 # Next.js App Router (76行)
├── page.tsx          # 主应用文件 (76行) - 使用usePageLogic统一逻辑
├── layout.tsx        # 全局布局 (31行)
├── globals.css       # 全局样式 (128行)
└── favicon.ico       # 网站图标

components/          # 组件化架构 (2,500+行) - 全面memo优化
├── Grid/            # 网格组件系统 (500+行) - 虚拟滚动支持
│   ├── GridContainer.tsx    # 主容器组件 (258行) - Phase 6.1性能优化
│   ├── GridCell.tsx         # 单元格组件 (211行) - memo+useMemo优化
│   ├── GridOverlay.tsx      # 覆盖层组件 (26行)
│   ├── types.ts             # 类型定义 (55行)
│   └── index.ts             # 统一导出 (5行)
└── ControlPanel/    # 控制面板组件系统 (2,000+行) - R2架构升级
    ├── ControlPanelContainer.tsx    # 主容器 (113行) - R2新架构支持
    ├── StylePanel.tsx               # R2样式面板 (254行)
    ├── BasicDataPanel.tsx           # R2基础数据面板 (397行)
    ├── CombinationBusinessPanel.tsx # R2组合业务面板 (490行)
    ├── ColorSystemPanel.tsx         # 统一颜色面板 (171行)
    ├── VersionPanel.tsx             # 版本管理 (138行)
    ├── ColorLevelToggle.tsx         # 级别切换 (85行)
    ├── ColorGroupSelector.tsx       # 分组选择 (100行)
    ├── types.ts                     # 类型定义 (77行)
    └── index.ts                     # 统一导出 (25行)

stores/              # Zustand状态管理 (4,000+行) - 5Store架构
├── styleStore.ts                    # 样式状态 (206行) - CSS映射管理
├── dynamicStyleStore.ts             # 动态样式状态 (240行) - UI配置管理
├── basicDataStore.ts                # 基础数据状态 (1,748行) - 颜色坐标核心
├── combinationDataStore.ts          # 组合数据状态 (570行) - 组合逻辑管理
├── businessDataStore.ts             # 业务数据状态 (845行) - 交互&版本管理
└── index.ts                         # 统一导出 (149行) - 架构集成

hooks/               # 自定义Hook (1,100+行) - 性能优化核心
├── usePageLogic.ts                  # 页面逻辑钩子 (522行) - 统一业务逻辑
└── useFormHandlers.ts               # 表单处理钩子 (604行) - 性能优化工具

utils/               # 工具函数模块 (1,200+行) - 高性能工具集
├── colorSystem.ts                   # 颜色系统工具 (199行) - ColorCoordinateIndex核心
├── dataConsistencyChecker.ts        # 数据一致性检查 (413行)
├── debugHelper.ts                   # 调试工具 (529行) - Debug Phase支持
├── groupLogicAnalyzer.ts            # 组合逻辑分析 (325行)
├── cellUtils.ts                     # 单元格工具 (56行)
├── styleUtils.ts                    # 样式工具 (56行)
├── buttonUtils.ts                   # 按钮工具 (56行)
└── colorUtils.ts                    # 颜色工具函数

types/               # TypeScript类型定义 (200+行)
├── color.ts                         # 颜色类型 (53行)
├── grid.ts                          # 网格类型 (26行)
└── version.ts                       # 版本类型 (109行)

constants/           # 常量管理系统 (分离架构)
├── colors.ts                        # 颜色常量 - COLOR_CSS_MAP等
└── styles.ts                        # 样式常量 - 按钮、面板样式

lib/
└── utils.ts                         # 基础工具函数 (7行) - cn等

__tests__/           # 测试文件
├── colorLevelDisplay.test.ts        # 颜色级别显示测试
└── debug_phase4_report.md           # 调试报告

docs/                # 项目文档
└── log250627/                       # 开发日志
    └── log_250627_1704.md          # 项目日志
```

### 2.2 组件拆分原则
1. **单一职责**: 每个组件只负责一个功能域
2. **行数限制**: 组件文件<500行，函数<100行
3. **类型安全**: 所有组件必须有完整的TypeScript接口
4. **性能优化**: 
   - 🚀 全面使用React.memo包装组件 (11个组件已优化)
   - ⚡ 大量使用useMemo和useCallback进行性能优化
   - 🎯 避免useState，统一使用Zustand状态管理
   - 🔄 虚拟滚动支持大网格渲染（GridContainer）

### 2.3 文件组织规范
```
📁 根据功能域组织文件
├── components/     # 按功能分组的组件 - memo优化
├── stores/        # 按业务域分离的状态 - 5Store架构
├── hooks/         # 自定义Hook - 性能优化核心
├── utils/         # 工具函数（ColorCoordinateIndex等高性能工具）
├── types/         # 类型定义
├── constants/     # 常量管理（分离架构）
└── __tests__/     # 测试文件

🎯 每个目录都有index.ts统一导出
📊 代码总行数约19,910行（排除node_modules）
```

### 2.4 关键文件
- [app/page.tsx](mdc:app/page.tsx) - 主页面组件 (76行) - 使用usePageLogic
- [hooks/usePageLogic.ts](mdc:hooks/usePageLogic.ts) - 页面逻辑Hook (522行) - 业务逻辑中枢
- [utils/colorSystem.ts](mdc:utils/colorSystem.ts) - 性能优化核心 (199行) - ColorCoordinateIndex
- [components/Grid/GridContainer.tsx](mdc:components/Grid/GridContainer.tsx) - 网格组件 (258行) - 虚拟滚动
- [components/ControlPanel/ControlPanelContainer.tsx](mdc:components/ControlPanel/ControlPanelContainer.tsx) - R2架构 (113行)
- [stores/basicDataStore.ts](mdc:stores/basicDataStore.ts) - 基础数据核心 (1,748行)
- [utils/debugHelper.ts](mdc:utils/debugHelper.ts) - Debug Phase支持 (529行)


