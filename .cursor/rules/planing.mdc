---
description: 
globs: 
alwaysApply: false
---
# 红色一级显示问题系统性修复计划

## 获取架构
- 已完成深度项目分析，发现问题不仅是单一函数修复，而是涉及memoization过度缓存、状态同步时序、CSS冲突等系统性问题
- 确认代码架构：React+TypeScript+Zustand+Tailwind，使用hooks模式和组件化设计

## 状态
- **进行中**

## 阶段
**1：核心渲染链路验证与修复（优先级最高）**
- [x] 验证GridCell组件中getCircleScaleStyle的实际调用链路
- [x] 检查usePageLogic.ts中memoization依赖项完整性
- [x] 在getCircleScaleStyle函数中添加红色一级特殊日志
- [x] 确认isLevelVisible函数调用参数正确性
- [x] 验证React memo和useMemo的缓存失效条件

**2：状态管理一致性保障（优先级高）**

- [x] 验证basicDataStore中DEFAULT_COLOR_VISIBILITY红色配置
- [x] 检查generateConsistentColorVisibility函数的红色处理
- [ ] 确认BasicDataPanel中红色级别控制UI与状态双向绑定
- [ ] 验证handleLevelToggle函数的红色一级处理
- [ ] 确保UI状态与Store状态实时同步

**3：调试工具体系化重构（优先级中）**
- [ ] 统一debugHelper.ts中所有红色一级调试函数
- [ ] 创建debugRedLevel1Suite综合诊断工具
- [ ] 新建utils/redLevel1Debugger.ts专门调试工具集
- [ ] 创建__tests__/redLevel1Integration.test.ts端到端测试
- [ ] 建立调试工具分级输出机制

**4：性能优化与监控强化（优先级低）**
- [ ] 优化hooks中的memoization策略
- [ ] 添加红色一级渲染性能监控点
- [ ] 实现GridContainer中红色一级特殊渲染优化
- [ ] 建立渲染异常自动恢复机制
- [ ] 完善TypeScript类型定义和约束

## 立即验证清单
- [ ] 浏览器控制台运行window.debug.diagnoseRedLevel1()
- [ ] 检查红色一级格子实际可见性状态
- [ ] 验证hooks/usePageLogic.ts:302行isLevelVisible调用
- [ ] 确认stores/basicDataStore.ts中红色showLevel1默认值

## 执行确认
若AI模型对话完毕后任务未全部完成，则更新执行情况，即对已执行任务打勾，状态：进行中
