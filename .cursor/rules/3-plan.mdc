---
description: 
globs: 
alwaysApply: false
---
# 用户提出需求或问题时，则执行：
- 清空`{项目根目录}/.cursor/rules/planing.mdc`
- 基于当前项目的代码架构和结构，生成任务清单，禁止直接修改代码：

## 要求
- 开始工作前，必须使用工具全面分析项目结构
- 任务规划须使用内置的任务管理工具（类似`add_tasks`, `update_tasks`, `view_tasklist`）
- 保存路径：`{项目根目录}/.cursor/rules/planing.mdc`（若 `.cursor/rules/` 目录不存在，则先创建该目录结构）

## 执行

   **深度项目分析**：
   - 分析：用工具分析项目架构、技术栈、核心模块、依赖关系、代码质量问题
   - 识别：性能瓶颈、技术债务、可维护性问题、安全隐患
   - 将发现的技术问题与用户具体需求进行关联分析

## 输出格式标准
使用以下极简任务格式（严格遵循格式，阶段数和子任务按需调整）：

```markdown
---
description: 执行任务
globs: 
alwaysApply: false
---

# 项目优化计划

## 获取架构
- 对项目充分分析，确保代码符合架构设计

## 状态
- **待开始**【已完成/进行中/待开始】

## 阶段
**1：xxx**
- [ ] 任务名称
- [ ] 任务名称

**2：xxx**
- [ ] 任务名称

**3：xxx**
- [ ] 任务名称 

## 执行确认
若AI模型对话完毕后任务未全部完成，则更新执行情况，即对已执行任务打勾，状态：进行中
```

