---
description: 
globs: 
alwaysApply: true
---
在每次对话结束前，获取当前系统时间后自动生成并保存项目开发日志文档。请按照以下规范执行：

 **文件保存规范：**
 - 保存路径：`{项目根目录}/docs/log{YYMMDD}/log_{YYMMDD}_{HHMM}.md`（如果 `docs/log{YYMMDD}/` 目录不存在，需要先创建该目录结构）
 - 文件名格式：使用24小时制时间格式，例如：`log_241227_1430.md`
 - 编码格式：UTF-8

 **开发日志模板：**
 ```markdown
 # 项目开发日志
 
 ## 用户提示词分析
 ◉ **原始提示词：** [完整记录用户的原始输入，如包含代码片段或错误信息，则提供简洁概述]
 ◉ **提示词优化建议：** [分析提示词的改进空间和优化方向]
 
 ## [任务标题] - 进度：[完成百分比]%
 
 ### 任务概况
 **开发者：** [AI模型名称]
 **任务类型：** [选择类型]
 - `feat`: 新功能开发
 - `fix`: Bug修复
 - `refactor`: 代码重构
 - `docs`: 文档更新
 - `style`: 代码格式调整
 - `test`: 测试相关
 - `chore`: 构建工具或辅助功能
 - `analysis`: 代码分析
 - `plan`: 项目规划
 **核心任务：** 
 - [主要任务描述]
 - [子任务1，如果有的话]
 - [子任务2，如果有的话]
 
 **完成摘要：** 
 - [100字以内的任务完成情况描述]
 
 ### 详细实施记录
 **问题背景/why：** 
 - [触发此次开发的原因或问题，100字以内]
 **实施内容/what：** 
 - [具体的代码修改、文件创建等操作，100字以内]
 **最终结果/how：** 
 - [任务完成后的实际效果和产出，100字以内]
 
 ### 技术要点
 **使用的工具/技术：** [列出主要使用的工具、框架、库等]
 **关键代码文件：** [列出主要修改或创建的文件路径，基于增、删、查、改的描述思路]
 **测试验证：** [描述如何验证功能正常工作]
 
 ### 后续计划
 **待办事项：** [列出未完成的任务，如果全部完成则写"无待办事项"]
 **改进建议：** [对当前实现的优化建议，如果无改进建议则写"当前实现已满足需求"]

 ```

 **执行要求：**
 1. 每次对话结束前必须生成此日志
 2. 标记为[...]的内容都必须填写具体信息
 3. 保持简洁性：每个描述部分控制在100字以内
 4. 确保时间戳准确反映对话结束时间
 5. 确保日志内容对后续开发具有参考价值，避免流水账式记录
