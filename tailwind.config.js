/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
    './hooks/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // 保留原有的颜色系统
      colors: {
        primary: {
          DEFAULT: '#1890ff',
          hover: '#40a9ff',
          50: '#e6f7ff',
          100: '#bae7ff',
          500: '#1890ff',  
          600: '#096dd9',
          700: '#0050b3'
        },
        success: '#52c41a',
        warning: '#faad14', 
        error: '#ff4d4f',
        
        // 文字颜色
        text: {
          primary: '#333333',
          secondary: '#666666',
          disabled: '#999999'
        },
        
        // 背景颜色
        bg: {
          primary: '#f0f2f5',
          surface: '#ffffff'
        },
        
        // 边框颜色
        border: {
          DEFAULT: '#e0e0e0',
          light: '#f0f0f0'
        },
        
        // 9种词汇颜色系统
        vocab: {
          gray: { bg: '#f5f5f5', border: '#d9d9d9' },
          red: { bg: '#fff2f0', border: '#ffccc7' },
          purple: { bg: '#f9f0ff', border: '#d3adf7' },
          cyan: { bg: '#e6fffb', border: '#87e8de' },
          yellow: { bg: '#feffe6', border: '#ffffb8' },
          pink: { bg: '#fff0f6', border: '#ffadd2' },
          blue: { bg: '#e6f7ff', border: '#91d5ff' },
          green: { bg: '#f6ffed', border: '#b7eb8f' },
          orange: { bg: '#fff7e6', border: '#ffd591' }
        }
      },
      
      // 保留原有的布局比例
      flex: {
        '1.4': '1.4 1 0%',
        '1.5': '1.5 1 0%', 
        '1.6': '1.6 1 0%'
      },
      
      // 保留原有的尺寸参数
      width: {
        '240': '240px',   // 小屏侧边栏
        '260': '260px',   // 中屏侧边栏
        '325': '325px',   // 标准侧边栏
        '420': '420px',   // 小屏词汇栏
        '480': '480px',   // 中屏词汇栏  
        '600': '600px'    // 标准词汇栏
      },
      
      // 阴影系统
      boxShadow: {
        'light': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'medium': '0 4px 12px rgba(0, 0, 0, 0.15)'
      },
      
      // 圆角系统
      borderRadius: {
        'DEFAULT': '6px',
        'small': '4px'
      },
      
      // 网格系统参数
      gridTemplateColumns: {
        '33': 'repeat(33, minmax(0, 1fr))',
        '16': 'repeat(16, minmax(0, 1fr))'
      },
      gridTemplateRows: {
        '33': 'repeat(33, minmax(0, 1fr))',
        '16': 'repeat(16, minmax(0, 1fr))'
      },
      
      // 动画持续时间
      transitionDuration: {
        '300': '300ms'
      },
      
      // 字体系统
      fontSize: {
        'xs': '0.75rem',
        'sm': '0.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem'
      },
      
      // 间距系统
      spacing: {
        '18': '4.5rem',
        '72': '18rem',
        '84': '21rem',
        '96': '24rem'
      }
    },
  },
  plugins: [],
} 