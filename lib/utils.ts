// 样式类合并函数（如果需要条件样式）
export function cn(...inputs: (string | undefined | false | null)[]): string {
  return inputs.filter(Boolean).join(' ')
}

// 样式类常量
export const styleClasses = {
  // 布局相关
  container: "flex w-full h-screen bg-gray-50 px-4",
  mainContent: "flex-[1.5] min-w-[600px] p-5 mr-3",
  sidebar: "w-[400px] bg-white border-l border-r border-gray-200 shadow-lg flex-shrink-0 mr-4",
  vocabularyPanel: "w-[700px] bg-white border-l border-gray-200 shadow-lg flex-shrink-0",
  
  // 按钮样式
  button: {
    primary: "bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded transition-colors",
    secondary: "bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded transition-colors",
    danger: "bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors",
    icon: "p-2 rounded hover:bg-gray-100 transition-colors"
  },
  
  // 输入框样式
  input: "w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-transparent",
  
  // 网格样式
  grid: "absolute inset-0 grid-33x33",
  gridCell: "cursor-pointer",
  
  // 词汇模块样式
  vocabularyModule: "mb-4 border rounded-lg overflow-hidden",
  vocabularyHeader: "flex items-center justify-between p-3 cursor-pointer select-none",
  vocabularyContent: "p-3 space-y-2",
  
  // 文本项样式
  textItem: "flex items-center space-x-2 p-2 rounded border transition-all",
  textInput: "flex-1 px-2 py-1 border rounded text-sm",
  
  // 页面项样式
  pageItem: "flex items-center space-x-2 p-3 border-b hover:bg-gray-50 cursor-pointer",
  pageItemActive: "bg-blue-50 border-l-4 border-l-primary",
  pageTitle: "flex-1 text-sm",
  pageTitleInput: "flex-1 px-2 py-1 text-sm border rounded"
}

// 颜色映射
export const colorClassMap = {
  'vocab-gray': { bg: 'bg-vocab-gray-bg', border: 'border-vocab-gray-border' },
  'vocab-red': { bg: 'bg-vocab-red-bg', border: 'border-vocab-red-border' },
  'vocab-purple': { bg: 'bg-vocab-purple-bg', border: 'border-vocab-purple-border' },
  'vocab-cyan': { bg: 'bg-vocab-cyan-bg', border: 'border-vocab-cyan-border' },
  'vocab-yellow': { bg: 'bg-vocab-yellow-bg', border: 'border-vocab-yellow-border' },
  'vocab-pink': { bg: 'bg-vocab-pink-bg', border: 'border-vocab-pink-border' },
  'vocab-blue': { bg: 'bg-vocab-blue-bg', border: 'border-vocab-blue-border' },
  'vocab-green': { bg: 'bg-vocab-green-bg', border: 'border-vocab-green-border' },
  'vocab-orange': { bg: 'bg-vocab-orange-bg', border: 'border-vocab-orange-border' }
}

// 字符串补零函数（兼容ES2015）
function padZero(num: number, len: number = 2): string {
  const str = String(num)
  return str.length >= len ? str : '0'.repeat(len - str.length) + str
}

// 时间格式化函数
export function formatTime(timeString: string | number[] | any): string {

  if (!timeString) {
    return ''
  }

  let date: Date

  // 处理Java LocalDateTime的不同序列化格式
  if (Array.isArray(timeString)) {
    // Java LocalDateTime序列化为数组格式: [2023, 6, 17, 14, 45, 10, 123456789]
    const [year, month, day, hour, minute, second, nano] = timeString
    date = new Date(year, month - 1, day, hour, minute, second, Math.floor(nano / 1000000))
  } else if (typeof timeString === 'string') {

    if (timeString.includes('T')) {
      // ISO格式: 2024-01-01T10:30:00 或 2024-01-01T10:30:00.123456789
      let processedTime = timeString
      if (timeString.includes('.') && timeString.split('.')[1].length > 3) {
        const parts = timeString.split('.')
        processedTime = parts[0] + '.' + parts[1].substring(0, 3)
      }
      date = new Date(processedTime)
    } else if (timeString.includes('-') && timeString.includes(':')) {
      // 格式: 2023-06-17 14:45:10
      const isoString = timeString.replace(' ', 'T')
      date = new Date(isoString)
    } else if (timeString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // 只有日期: 2023-06-17
      date = new Date(timeString + 'T00:00:00')
    } else {
      date = new Date(timeString)
    }
  } else if (typeof timeString === 'number') {
    date = new Date(timeString)
  } else if (typeof timeString === 'object' && timeString !== null) {

    if (timeString.year && timeString.monthValue && timeString.dayOfMonth) {
      const year = timeString.year
      const month = timeString.monthValue - 1
      const day = timeString.dayOfMonth
      const hour = timeString.hour || 0
      const minute = timeString.minute || 0
      const second = timeString.second || 0
      const nano = timeString.nano || 0

      date = new Date(year, month, day, hour, minute, second, Math.floor(nano / 1000000))
    } else {
      date = new Date(timeString)
    }
  } else {
    date = new Date(timeString)
  }

  if (isNaN(date.getTime())) {
    return String(timeString)
  }

  // 格式化为 MM-DD HH:mm
  const month = padZero(date.getMonth() + 1)
  const day = padZero(date.getDate())
  const hours = padZero(date.getHours())
  const minutes = padZero(date.getMinutes())

  const formatted = `${month}-${day} ${hours}:${minutes}`
  return formatted
}

// 生成唯一ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
}

