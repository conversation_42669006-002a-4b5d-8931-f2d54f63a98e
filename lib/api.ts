import type {
  TextInfo,
  TextUpdateRequest,
  TextUpdateResponse
} from './types'

const API_BASE_URL = 'http://localhost:8080'

// API请求封装
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
  }

  return response.json()
}

// 文本信息API
export const textApi = {
  // 获取所有文本信息
  async find(): Promise<TextInfo[]> {
    return apiRequest<TextInfo[]>('/text/find')
  },

  // 更新文本信息
  async update(data: TextUpdateRequest): Promise<TextUpdateResponse> {
    return apiRequest<TextUpdateResponse>('/text/update', {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },
}


