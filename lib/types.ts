// 页面数据结构
export interface Page {
  id: string;                   // 页面唯一ID（字符串格式避免精度丢失）
  title: string;                // 页面标题
  elements: Element[];          // 页面元素数组
  createTime: string | number[]; // 创建时间（支持多种格式）
  gridData: Record<string, GridCellData>; // 网格数据存储
}

// 网格单元格数据
export interface GridCellData {
  clickCount: number;
  data?: any;
}

// 网格点击信息
export interface CellInfo {
  row: number;
  col: number;
  pageId: string;
  cellId: string;
}

// 页面元素类型
export interface Element {
  id: string;
  type: 'text' | 'image' | 'shape' | 'video';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  zIndex?: number;
  opacity?: number;
  // 文本元素属性
  content?: string;
  fontSize?: number;
  color?: string;
  fontWeight?: string;
  textAlign?: string;
  // 图片/视频元素属性
  src?: string;
  alt?: string;
  // 形状元素属性
  shape?: 'rectangle' | 'circle' | 'triangle';
  borderRadius?: string;
  border?: string;
}

// 词汇相关类型
export interface TextItem {
  id: string;                   // 前端临时ID
  phraseId: string | null;      // 后端数据库ID
  content: string;              // 词汇内容
  editing: boolean;             // 编辑状态
  originalContent: string;      // 原始内容
  selected: boolean;            // 选中状态
  wordReplacing: boolean;       // 词汇替换动画标记
  dragging: boolean;            // 拖拽状态标记
}

export interface VocabularyModule {
  id: string;
  colorClass: string;
  expanded: boolean;
  textItems: TextItem[];
}

// 后端API类型 - 匹配实际返回的字段名
export interface Table {
  id: number;                   // 数据库主键ID
  name: string;                 // 页面名称
  createTime: string;           // 后端返回的时间字段名
}

export interface PhraseDto {
  word: string;                 // 词汇内容
  color: number;                // 颜色索引(0-8)
  tableId: string;              // 关联页面ID
  beforeId: string | null;      // 前一个词汇ID（排序用）
  afterId: string | null;       // 后一个词汇ID（排序用）
  sort: number;                 // 排序值
}

export interface Phrase {
  id: string;                   // 数据库主键ID
  word: string;                 // 词汇内容
  color: number;                // 颜色索引
  tableId: string;              // 关联页面ID
  beforeId: string | null;      // 前置词汇ID
  afterId: string | null;       // 后置词汇ID
  sort: number;                 // 排序值
  createTime: string | number[]; // 创建时间
}

// 统一响应格式
export interface ApiResponse<T> {
  code: number;        // 状态码：1=成功，其他=失败
  data: T;            // 实际数据
  message: string;    // 消息说明
}

// 颜色系统
export const COLOR_CLASSES = [
  'vocab-gray',    // 0 - 灰色
  'vocab-red',     // 1 - 红色
  'vocab-purple',  // 2 - 紫色
  'vocab-cyan',    // 3 - 青色
  'vocab-yellow',  // 4 - 黄色
  'vocab-pink',    // 5 - 粉色
  'vocab-blue',    // 6 - 蓝色
  'vocab-green',   // 7 - 绿色
  'vocab-orange'   // 8 - 橙色
] as const

export type ColorClass = typeof COLOR_CLASSES[number]

// 词汇颜色类型
export type VocabularyColor = 'yellow' | 'orange' | 'red' | 'pink' | 'purple' | 'blue' | 'green' | 'brown' | 'gray' 

// 文本信息API类型
export interface TextInfo {
  id: string;     // 使用字符串避免大整数精度丢失
  color: number;  // 0-8，对应颜色：灰0、红1、紫2、青3、黄4、粉5、蓝6、绿7、橙8
  text: string;
}

// 文本更新请求类型
export interface TextUpdateRequest {
  id: string;     // 必需，后端通过ID精确定位记录（使用字符串避免大整数精度丢失）
  color: number;  // 0-8，颜色标识
  text: string;   // 新的文本内容
}

// 文本更新响应类型（返回完整的TextInfo对象）
export interface TextUpdateResponse extends TextInfo {
  // 继承TextInfo的所有字段：id, color, text
}





// 新增：坐标数据类型
export interface Coordinate {
  id: number;                   // 数据库主键ID
  tableId: number;              // 关联页面ID
  color: number;                // 颜色值
  position: string;             // 位置信息
  voc: string;                  // 显示的文本内容
  repeated: number;             // 重复次数
} 