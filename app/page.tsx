'use client'

import { useState, useEffect } from 'react'
import Sidebar from '@/components/Sidebar'
import SlideView from '@/components/SlideView'
import VocabularyPanel from '@/components/VocabularyPanel'
import { useVocabularyStore } from '@/hooks/useVocabularyStore'
import { styleClasses } from '@/lib/utils'

export default function HomePage() {
  const { initializeModules } = useVocabularyStore()
  const [isSlideViewSelected, setIsSlideViewSelected] = useState(false)

  useEffect(() => {
    initializeModules()
  }, [initializeModules])

  return (
    <div className={styleClasses.container}>
      {/* 主内容区域 */}
      <div className={styleClasses.mainContent}>
        <SlideView 
          isSelected={isSlideViewSelected}
          onSelectionChange={setIsSlideViewSelected}
        />
      </div>
      
      {/* 页面管理侧边栏 */}
      <div className={styleClasses.sidebar}>
        <Sidebar />
      </div>

      {/* 词汇管理面板 */}
      <div className={styleClasses.vocabularyPanel}>
        <VocabularyPanel />
      </div>
    </div>
  )
} 