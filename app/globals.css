@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义全局样式 */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* 网格样式 */
.grid-cell {
  transition: all 0.2s ease;
  border: 0.5px solid rgba(255, 255, 255, 0.3);
  background-color: transparent;
}

.grid-cell:hover {
  background-color: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.6);
}

.grid-cell.clicked {
  background-color: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.8);
}

/* 确保网格容器正确显示 */
.grid-33x33 {
  display: grid;
  grid-template-columns: repeat(33, 1fr);
  grid-template-rows: repeat(33, 1fr);
  width: 100%;
  height: 100%;
}

/* 词汇颜色样式 */
.vocab-gray { @apply bg-gray-100 border-gray-300; }
.vocab-red { @apply bg-red-50 border-red-200; }
.vocab-purple { @apply bg-purple-50 border-purple-200; }
.vocab-cyan { @apply bg-cyan-50 border-cyan-200; }
.vocab-yellow { @apply bg-yellow-50 border-yellow-200; }
.vocab-pink { @apply bg-pink-50 border-pink-200; }
.vocab-blue { @apply bg-blue-50 border-blue-200; }
.vocab-green { @apply bg-green-50 border-green-200; }
.vocab-orange { @apply bg-orange-50 border-orange-200; }

/* 拖拽相关样式 */
.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.drop-zone {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed rgba(59, 130, 246, 0.5);
}

/* 动画 */
.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} 