import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '幻灯片编辑器 - Slide Editor',
  description: '基于Next.js构建的现代化幻灯片编辑器，支持多页面管理、网格交互系统、词汇管理和拖拽操作',
  keywords: ['幻灯片编辑器', 'Next.js', 'React', 'TypeScript', 'Tailwind CSS'],
  authors: [{ name: 'Slide Editor Team' }],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={inter.className}>
        <div id="root" className="w-full h-screen overflow-hidden">
          {children}
        </div>
      </body>
    </html>
  )
} 