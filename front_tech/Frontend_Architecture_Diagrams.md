# Cube Frontend 系统架构图表 (简化版)

## 📋 概述

本文档包含Cube Frontend系统的三个核心架构图表，展示了简化后的系统架构。

## 🔄 简化说明

**核心功能：**
- ✅ **三栏布局** - 保持侧边栏+主视图+词汇面板的布局
- ✅ **UI交互** - 保留网格点击、文本编辑等基础交互
- ✅ **动画效果** - 保留过渡动画和视觉反馈
- ✅ **状态管理** - 保留Zustand基础状态管理
- ✅ **样式系统** - 保留Tailwind CSS和工具函数
- ✅ **API集成** - 集成文本信息管理API
- ✅ **数据持久化** - 支持文本数据的加载和保存

---

## 🏗️ 1. 技术架构图

展示了Cube Frontend系统的整体架构，包括各个模块之间的关系和数据流向。系统采用分层架构设计，包含UI层、组件层、状态管理层等。

```mermaid
graph TB
    subgraph "用户界面层"
        UI[用户界面]
        UI --> Page[app/page.tsx<br/>主页面组件]
        UI --> Layout[app/layout.tsx<br/>根布局组件]
    end

    subgraph "组件层"
        Page --> SlideView[SlideView.tsx<br/>幻灯片视图<br/>• 33x33网格系统<br/>• 背景图片显示<br/>• 网格交互]
        Page --> Sidebar[Sidebar.tsx<br/>侧边栏<br/>• 页面管理<br/>• 导航控制]
        Page --> VocabPanel[VocabularyPanel.tsx<br/>词汇面板<br/>• 9色分类容器<br/>• 文本编辑<br/>• 实时顿号监听<br/>• 智能光标定位<br/>• API集成]
    end

    subgraph "状态管理层"
        SlideView --> SlideStore[useSlideStore.ts<br/>页面状态管理<br/>• 页面列表<br/>• 活动页面<br/>• 网格数据]
        VocabPanel --> VocabStore[useVocabularyStore.ts<br/>词汇状态管理<br/>• 词汇模块<br/>• 文本项目<br/>• 编辑状态]

        SlideStore --> Zustand1[Zustand Store<br/>页面状态]
        VocabStore --> Zustand2[Zustand Store<br/>词汇状态]
    end

    subgraph "API服务层"
        VocabPanel --> TextAPI[textApi<br/>• find: 获取文本<br/>• update: 更新文本]
        VocabPanel --> PhraseAPI[phraseApi<br/>• add: 实时添加词汇<br/>• 返回完整textInfo]
        TextAPI --> APICore[lib/api.ts<br/>API请求封装<br/>• HTTP请求<br/>• 错误处理]
        PhraseAPI --> APICore
    end

    subgraph "工具层"
        SlideStore --> Types[lib/types.ts<br/>类型定义<br/>• 接口定义<br/>• 数据模型<br/>• API类型]
        VocabStore --> Utils[lib/utils.ts<br/>工具函数<br/>• 样式工具<br/>• 时间格式化]
        SlideView --> Utils
        APICore --> Types
    end

    subgraph "后端服务"
        APICore --> Backend[FastAPI 后端<br/>localhost:8080<br/>• 表格管理<br/>• 词汇处理<br/>• 文本管理<br/>• 坐标管理]
        Backend --> Database[(SQLite 数据库<br/>• table 表<br/>• phrase 表<br/>• text 表<br/>• coordinate 表)]
    end

    subgraph "配置层"
        Config[next.config.js<br/>• API代理<br/>• CORS配置<br/>• 静态资源]
        Tailwind[tailwind.config.js<br/>• 样式配置<br/>• 主题定制]
        TypeScript[tsconfig.json<br/>• TypeScript配置<br/>• 路径映射]
    end

    %% 数据流向
    SlideView -.->|网格点击事件| SlideStore
    VocabPanel -.->|文本编辑事件| VocabStore
    SlideStore -.->|页面切换| VocabPanel

    %% 配置关系
    Types --> Config
    Utils --> Config
    Page --> TypeScript
    SlideStore --> TypeScript
    VocabStore --> TypeScript

    classDef uiLayer fill:#e1f5fe
    classDef componentLayer fill:#f3e5f5
    classDef stateLayer fill:#e8f5e8
    classDef utilLayer fill:#fce4ec
    classDef configLayer fill:#e0f2f1

    class UI,Page,Layout uiLayer
    class SlideView,Sidebar,VocabPanel componentLayer
    class SlideStore,VocabStore,Zustand1,Zustand2 stateLayer
    class Types,Utils utilLayer
    class Config,Tailwind,TypeScript configLayer
```

---

## 🔄 2. 业务流程图

展示了系统主要业务流程，包括页面管理、网格交互、词汇管理等核心功能的处理流程。

```mermaid
graph TD
    Start([用户访问系统]) --> LoadApp[加载应用]
    LoadApp --> InitPages[初始化页面列表]
    InitPages --> LoadFirstPage[加载第一个页面]
    LoadFirstPage --> MainInterface[显示主界面]

    MainInterface --> PageManagement[页面管理]
    MainInterface --> GridInteraction[网格交互]
    MainInterface --> VocabManagement[词汇管理]

    subgraph "页面管理流程"
        PageManagement --> PageOp{操作类型}
        PageOp -->|创建| PageAdd[创建新页面]
        PageOp -->|切换| PageSwitch[切换页面]
        PageOp -->|删除| PageDelete[删除页面]
        PageOp -->|重命名| PageRename[重命名页面]
        
        PageAdd --> UpdatePageList[更新页面列表]
        PageSwitch --> UpdateActiveState[更新活动状态]
        PageDelete --> RemoveFromList[从列表移除]
        PageRename --> UpdatePageTitle[更新页面标题]

        UpdatePageList --> PageSuccess[操作成功]
        UpdateActiveState --> PageSuccess
        RemoveFromList --> PageSuccess
        UpdatePageTitle --> PageSuccess
    end

    subgraph "网格交互流程"
        GridInteraction --> ClickGrid[点击网格单元格]
        ClickGrid --> NormalClick[普通点击]
        NormalClick --> UpdateGrid[更新网格状态]
        UpdateGrid --> ShowClickCount[显示点击次数]
        ShowClickCount --> PageSuccess
    end

    subgraph "词汇管理流程"
        VocabManagement --> LoadData[页面加载获取数据]
        LoadData --> CallFindAPI[调用 /text/find API]
        CallFindAPI --> MapColorData[按color字段映射数据]
        MapColorData --> SaveBackendIds[保存后端ID映射]
        SaveBackendIds --> DisplayTexts[显示文本内容]

        VocabManagement --> DoubleClickEdit[双击容器编辑]
        DoubleClickEdit --> SmartCursor[智能光标定位]
        SmartCursor --> EditMode[进入编辑模式]
        EditMode --> StartMonitoring[启动实时监听]
        StartMonitoring --> TextInput[文本输入]

        TextInput --> CommaDetection{检测顿号新增?}
        CommaDetection -->|检测到顿号| RealtimeProcess[实时处理流程]
        CommaDetection -->|无顿号| SaveAction{保存操作}

        subgraph "实时顿号处理"
            RealtimeProcess --> CalcCursorPos[计算光标在第几个顿号后]
            CalcCursorPos --> CallPhraseAPI[调用 /phrase/add API<br/>传递color+new_text]
            CallPhraseAPI --> GetTextInfo[获取返回的text_info对象]
            GetTextInfo --> DiffDetection[智能差异检测算法<br/>分析文本变化<br/>计算局部更新]
            DiffDetection --> ApplyDifference[应用文本差异<br/>只更新变化部分<br/>自动调整光标位置]
            ApplyDifference --> ContinueEdit[保持编辑状态<br/>用户可继续输入<br/>无跳跃感]
        end

        ContinueEdit --> TextInput
        TextInput --> SaveAction{保存操作}
        SaveAction -->|Enter键| SaveText[保存文本]
        SaveAction -->|失焦| SaveText
        SaveAction -->|Escape键| CancelEdit[取消编辑]

        SaveText --> TextChangeCheck{文本是否变化?}
        TextChangeCheck -->|有变化| CallUpdateAPI[调用 /text/update API<br/>传递ID+color+text]
        TextChangeCheck -->|无变化| SkipAPI[跳过API调用]
        CallUpdateAPI --> UpdateLocalState[更新本地状态]
        SkipAPI --> UpdateLocalState
        UpdateLocalState --> TextSuccess[保存成功]

        CancelEdit --> RestoreOriginal[恢复原始值]
        RestoreOriginal --> TextSuccess
    end

    PageSuccess --> MainInterface
    InsertWord --> MainInterface
    UnlockInput1 --> MainInterface
    UnlockInput2 --> MainInterface
    TextSuccess --> MainInterface

    classDef startEnd fill:#4caf50,stroke:#2e7d32,color:#fff
    classDef process fill:#2196f3,stroke:#1565c0,color:#fff
    classDef decision fill:#ff9800,stroke:#ef6c00,color:#fff
    classDef api fill:#9c27b0,stroke:#6a1b9a,color:#fff
    classDef success fill:#8bc34a,stroke:#558b2f,color:#fff

    class Start,MainInterface startEnd
    class LoadApp,InitPages,LoadFirstPage,PageAdd,PageSwitch,PageDelete,PageRename,UpdatePageList,UpdateActiveState,RemoveFromList,UpdatePageTitle,ClickGrid,NormalClick,UpdateGrid,ShowClickCount,LoadData,MapColorData,SaveBackendIds,DisplayTexts,DoubleClickEdit,SmartCursor,EditMode,StartMonitoring,TextInput,SaveText,UpdateLocalState,CancelEdit,RestoreOriginal,SkipAPI,RealtimeProcess,CalcCursorPos,GetTextInfo,DiffDetection,ApplyDifference,ContinueEdit process
    class PageOp,SaveAction,TextChangeCheck,CommaDetection decision
    class CallFindAPI,CallUpdateAPI,CallPhraseAPI api
    class PageSuccess,TextSuccess success
```

---

## 🗄️ 3. 状态管理关系图

展示了简化后的Zustand全局状态管理和组件本地状态的基础关系。

```mermaid
graph TB
    subgraph "全局状态层"
        SlideStore["useSlideStore<br/>页面状态管理<br/>• pages: 页面数组<br/>• activePage: 活动页面索引"]

        VocabStore["useVocabularyStore<br/>词汇状态管理<br/>• modules: 词汇模块数组"]

        SlideStore --> ZustandCore["Zustand Core<br/>DevTools集成"]
        VocabStore --> ZustandCore
    end

    subgraph "组件本地状态层"
        subgraph "SlideView 组件状态"
            SlideLocalState["useState Hook<br/>• selectedGrid: 网格位置对象"]
        end

        subgraph "VocabularyPanel 组件状态"
            VocabLocalState["useState Hook<br/>• texts: 文本记录对象<br/>• textIds: 后端ID映射<br/>• editingId: 编辑ID<br/>• editingValue: 编辑值<br/>• originalValue: 原始值<br/>• collapsedContainers: 折叠容器集合<br/>• loading: 加载状态<br/>• targetCursorPosition: 目标光标位置<br/>• isRealTimeMonitoring: 实时监听开关<br/>• lastCommaCount: 上次顿号数量<br/>• textareaRef: 文本框引用<br/>• originalTextRef: 原始文本引用"]
        end

        subgraph "Sidebar 组件状态"
            SidebarLocalState["useState Hook<br/>• editingIndex: number<br/>• editingTitle: string<br/>• isEnterPressed: boolean"]
        end
    end

    subgraph "状态操作层"
        subgraph "SlideStore Actions"
            SlideActions["页面操作方法<br/>• addPage<br/>• deletePage<br/>• updatePageTitle<br/>• setActivePage<br/>• handleCellClick"]
        end

        subgraph "VocabStore Actions"
            VocabActions["词汇操作方法<br/>• addTextItem<br/>• updateTextItem<br/>• deleteTextItem<br/>• toggleModule<br/>• startEditing"]
        end

        subgraph "API Actions"
            APIActions["API操作方法<br/>• loadTextData<br/>• saveTextData<br/>• realtimeCommaDetection<br/>• calculateCommaPosition<br/>• calculateNewCursorPosition<br/>• smartCursorPositioning<br/>• handleAPIError"]
        end
    end

    %% 状态流向关系
    SlideStore -.->|状态订阅| SlideView["SlideView 组件"]
    SlideStore -.->|状态订阅| Sidebar["Sidebar 组件"]
    VocabStore -.->|状态订阅| VocabPanel["VocabularyPanel 组件"]

    SlideView -.->|状态更新| SlideActions
    Sidebar -.->|状态更新| SlideActions
    VocabPanel -.->|状态更新| VocabActions
    VocabPanel -.->|API调用| APIActions

    SlideActions -.->|状态变更| SlideStore
    VocabActions -.->|状态变更| VocabStore
    APIActions -.->|数据更新| VocabPanel

    %% 简单的页面切换通信
    SlideStore -.->|页面切换| VocabPanel

    classDef globalState fill:#e8f5e8
    classDef localState fill:#e3f2fd
    classDef actions fill:#fff3e0
    classDef components fill:#fafafa

    class SlideStore,VocabStore,ZustandCore globalState
    class SlideLocalState,VocabLocalState,SidebarLocalState localState
    class SlideActions,VocabActions,APIActions actions
    class SlideView,Sidebar,VocabPanel components
```

---

## 📊 图表说明

### 技术架构图特点
- **分层设计**: 清晰的分层架构，职责分离
- **组件化**: React组件化开发模式
- **状态管理**: Zustand全局状态管理

### 业务流程图特点
- **完整流程**: 覆盖所有主要业务场景
- **AI处理**: 智能词汇处理机制
- **用户交互**: 流畅的操作体验

### 状态管理关系图特点
- **响应式**: 状态变化自动触发更新
- **类型安全**: 完整的TypeScript类型约束
- **开发友好**: Redux DevTools集成

---

## 🛠️ 技术栈

- **前端框架**: Next.js 14 - React全栈框架
- **UI库**: React 18 - 用户界面库
- **状态管理**: Zustand - 轻量级状态管理
- **类型系统**: TypeScript - 静态类型检查
- **样式框架**: Tailwind CSS - 原子化CSS框架
- **构建工具**: Next.js - 内置构建优化

---

## 📞 联系信息

如有架构相关问题，请联系开发团队。

**文档版本**: 1.0.0
**最后更新**: 2025-07-17
