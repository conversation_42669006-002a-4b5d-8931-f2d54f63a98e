<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube Frontend 系统架构图表 (简化版)</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .nav {
            background: #f8f9fa;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            position: sticky;
            top: 140px;
            z-index: 99;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }
        
        .nav a {
            color: #495057;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .nav a:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .content {
            padding: 3rem 2rem;
        }
        
        .diagram-section {
            margin-bottom: 5rem;
            scroll-margin-top: 220px;
        }
        
        .diagram-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 4px solid #667eea;
            display: inline-block;
            position: relative;
        }
        
        .diagram-title::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 50%;
            height: 4px;
            background: #764ba2;
        }
        
        .diagram-description {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.8;
            font-size: 1.2rem;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        
        .mermaid {
            background: #fafafa;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 15px;
            margin: 3rem 0;
            border: 1px solid #dee2e6;
        }
        
        .tech-stack h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }
        
        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .tech-item {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .tech-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .tech-item strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .tech-item span {
            color: #666;
            font-size: 0.95rem;
        }
        
        .features {
            background: #e3f2fd;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 1px solid #2196f3;
        }
        
        .features h4 {
            color: #1976d2;
            margin-bottom: 1rem;
            font-size: 1.4rem;
        }
        
        .features ul {
            list-style: none;
            padding-left: 0;
        }
        
        .features li {
            padding: 0.5rem 0;
            position: relative;
            padding-left: 2rem;
        }
        
        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 3rem 2rem;
            margin-top: 4rem;
        }
        
        .footer p {
            margin: 0.5rem 0;
            font-size: 1.1rem;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 2rem 1rem;
            }
            
            .nav {
                padding: 1rem;
            }
            
            .nav ul {
                flex-direction: column;
                gap: 1rem;
            }
            
            .diagram-title {
                font-size: 2rem;
            }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 12px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        /* 动画效果 */
        .diagram-section {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }
        
        .diagram-section:nth-child(1) { animation-delay: 0.2s; }
        .diagram-section:nth-child(2) { animation-delay: 0.4s; }
        .diagram-section:nth-child(3) { animation-delay: 0.6s; }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 Cube Frontend 系统架构图表</h1>
            <p>展示前端系统架构，包含API集成、文本管理和数据持久化功能</p>
        </header>
        
        <nav class="nav">
            <ul>
                <li><a href="#architecture">🏗️ 技术架构图</a></li>
                <li><a href="#workflow">🔄 业务流程图</a></li>
                <li><a href="#state">🗄️ 状态管理关系图</a></li>
            </ul>
        </nav>
        
        <main class="content">
            <!-- 技术架构图 -->
            <section id="architecture" class="diagram-section">
                <h2 class="diagram-title">🏗️ 技术架构图</h2>
                <div class="diagram-description">
                    展示了Cube Frontend系统的整体架构，包括各个模块之间的关系和数据流向。
                    系统采用分层架构设计，包含UI层、组件层、状态管理层等。
                </div>
                <div class="mermaid">
graph TB
    subgraph "用户界面层"
        UI[用户界面]
        UI --> Page[app/page.tsx<br/>主页面组件]
        UI --> Layout[app/layout.tsx<br/>根布局组件]
    end

    subgraph "组件层"
        Page --> SlideView[SlideView.tsx<br/>幻灯片视图<br/>• 33x33网格系统<br/>• 背景图片显示<br/>• 网格交互]
        Page --> Sidebar[Sidebar.tsx<br/>侧边栏<br/>• 页面管理<br/>• 导航控制]
        Page --> VocabPanel[VocabularyPanel.tsx<br/>词汇面板<br/>• 9色分类容器<br/>• 文本编辑<br/>• API集成]
    end

    subgraph "状态管理层"
        SlideView --> SlideStore[useSlideStore.ts<br/>页面状态管理<br/>• 页面列表<br/>• 活动页面<br/>• 网格数据]
        VocabPanel --> VocabStore[useVocabularyStore.ts<br/>词汇状态管理<br/>• 词汇模块<br/>• 文本项目<br/>• 编辑状态]

        SlideStore --> Zustand1[Zustand Store<br/>页面状态]
        VocabStore --> Zustand2[Zustand Store<br/>词汇状态]
    end

    subgraph "API服务层"
        VocabPanel --> TextAPI[textApi<br/>• find: 获取文本<br/>• update: 更新文本]
        TextAPI --> APICore[lib/api.ts<br/>API请求封装<br/>• HTTP请求<br/>• 错误处理]
    end

    subgraph "工具层"
        SlideStore --> Types[lib/types.ts<br/>类型定义<br/>• 接口定义<br/>• 数据模型<br/>• API类型]
        VocabStore --> Utils[lib/utils.ts<br/>工具函数<br/>• 样式工具<br/>• 时间格式化]
        SlideView --> Utils
        APICore --> Types
    end

    subgraph "后端服务"
        APICore --> Backend[FastAPI 后端<br/>localhost:8080<br/>• 表格管理<br/>• 词汇处理<br/>• 文本管理<br/>• 坐标管理]
        Backend --> Database[(SQLite 数据库<br/>• table 表<br/>• phrase 表<br/>• text 表<br/>• coordinate 表)]
    end

    subgraph "配置层"
        Config[next.config.js<br/>• API代理<br/>• CORS配置<br/>• 静态资源]
        Tailwind[tailwind.config.js<br/>• 样式配置<br/>• 主题定制]
        TypeScript[tsconfig.json<br/>• TypeScript配置<br/>• 路径映射]
    end

    %% 数据流向
    SlideView -.->|网格点击事件| SlideStore
    VocabPanel -.->|文本编辑事件| VocabStore
    SlideStore -.->|页面切换| VocabPanel

    %% 配置关系
    Types --> Config
    Utils --> Config
    Page --> TypeScript
    SlideStore --> TypeScript
    VocabStore --> TypeScript

    classDef uiLayer fill:#e1f5fe
    classDef componentLayer fill:#f3e5f5
    classDef stateLayer fill:#e8f5e8
    classDef utilLayer fill:#fce4ec
    classDef configLayer fill:#e0f2f1

    class UI,Page,Layout uiLayer
    class SlideView,Sidebar,VocabPanel componentLayer
    class SlideStore,VocabStore,Zustand1,Zustand2 stateLayer
    class Types,Utils utilLayer
    class Config,Tailwind,TypeScript configLayer
                </div>

                <div class="features">
                    <h4>技术架构图特点</h4>
                    <ul>
                        <li><strong>分层设计</strong>: 清晰的分层架构，职责分离</li>
                        <li><strong>组件化</strong>: React组件化开发模式</li>
                        <li><strong>状态管理</strong>: Zustand全局状态管理</li>
                    </ul>
                </div>
            </section>

            <!-- 业务流程图 -->
            <section id="workflow" class="diagram-section">
                <h2 class="diagram-title">🔄 业务流程图</h2>
                <div class="diagram-description">
                    展示了系统主要业务流程，包括页面管理、网格交互、词汇管理等核心功能的处理流程。
                    涵盖了完整的业务操作链路和AI处理机制。
                </div>
                <div class="mermaid">
graph TD
    Start([用户访问系统]) --> LoadApp[加载应用]
    LoadApp --> InitPages[初始化页面列表]
    InitPages --> LoadFirstPage[加载第一个页面]
    LoadFirstPage --> MainInterface[显示主界面]

    MainInterface --> PageManagement[页面管理]
    MainInterface --> GridInteraction[网格交互]
    MainInterface --> VocabManagement[词汇管理]

    subgraph "页面管理流程"
        PageManagement --> PageOp{操作类型}
        PageOp -->|创建| PageAdd[创建新页面]
        PageOp -->|切换| PageSwitch[切换页面]
        PageOp -->|删除| PageDelete[删除页面]
        PageOp -->|重命名| PageRename[重命名页面]

        PageAdd --> UpdatePageList[更新页面列表]
        PageSwitch --> UpdateActiveState[更新活动状态]
        PageDelete --> RemoveFromList[从列表移除]
        PageRename --> UpdatePageTitle[更新页面标题]

        UpdatePageList --> PageSuccess[操作成功]
        UpdateActiveState --> PageSuccess
        RemoveFromList --> PageSuccess
        UpdatePageTitle --> PageSuccess
    end

    subgraph "网格交互流程"
        GridInteraction --> ClickGrid[点击网格单元格]
        ClickGrid --> NormalClick[普通点击]
        NormalClick --> UpdateGrid[更新网格状态]
        UpdateGrid --> ShowClickCount[显示点击次数]
        ShowClickCount --> PageSuccess
    end

    subgraph "词汇管理流程"
        VocabManagement --> LoadData[页面加载获取数据]
        LoadData --> CallFindAPI[调用 /text/find API]
        CallFindAPI --> MapColorData[按color字段映射数据]
        MapColorData --> DisplayTexts[显示文本内容]

        VocabManagement --> DoubleClickEdit[双击容器编辑]
        DoubleClickEdit --> SmartCursor[智能光标定位]
        SmartCursor --> EditMode[进入编辑模式]
        EditMode --> TextInput[文本输入]

        TextInput --> SaveAction{保存操作}
        SaveAction -->|Enter键| SaveText[保存文本]
        SaveAction -->|失焦| SaveText
        SaveAction -->|Escape键| CancelEdit[取消编辑]

        SaveText --> TextChangeCheck{文本是否变化?}
        TextChangeCheck -->|有变化| CallUpdateAPI[调用 /text/update API]
        TextChangeCheck -->|无变化| SkipAPI[跳过API调用]
        CallUpdateAPI --> UpdateLocalState[更新本地状态]
        SkipAPI --> UpdateLocalState
        UpdateLocalState --> VocabSuccess[保存成功]

        CancelEdit --> RestoreOriginal[恢复原始值]
        RestoreOriginal --> VocabSuccess
    end

    PageSuccess --> MainInterface
    UpdateGridState --> MainInterface
    InsertWord --> MainInterface
    CancelSelection --> MainInterface
    VocabSuccess --> MainInterface

    classDef startEnd fill:#4caf50,stroke:#2e7d32,color:#fff
    classDef process fill:#2196f3,stroke:#1565c0,color:#fff
    classDef decision fill:#ff9800,stroke:#ef6c00,color:#fff
    classDef api fill:#9c27b0,stroke:#6a1b9a,color:#fff
    classDef success fill:#8bc34a,stroke:#558b2f,color:#fff

    class Start,MainInterface startEnd
    class LoadApp,InitPages,LoadFirstPage,PageAdd,PageSwitch,PageDelete,PageRename,UpdatePageList,UpdateActiveState,RemoveFromList,UpdatePageTitle,ClickGrid,NormalClick,UpdateGrid,ShowClickCount,LoadData,MapColorData,DisplayTexts,DoubleClickEdit,SmartCursor,EditMode,TextInput,SaveText,UpdateLocalState,CancelEdit,RestoreOriginal,SkipAPI process
    class PageOp,SaveAction,TextChangeCheck decision
    class CallFindAPI,CallUpdateAPI api
    class PageSuccess,VocabSuccess success
                </div>

                <div class="features">
                    <h4>业务流程图特点</h4>
                    <ul>
                        <li><strong>完整流程</strong>: 覆盖所有主要业务场景</li>
                        <li><strong>AI处理</strong>: 智能词汇处理机制</li>
                        <li><strong>用户交互</strong>: 流畅的操作体验</li>
                    </ul>
                </div>
            </section>

            <!-- 状态管理关系图 -->
            <section id="state" class="diagram-section">
                <h2 class="diagram-title">🗄️ 状态管理关系图</h2>
                <div class="diagram-description">
                    展示了简化后的Zustand全局状态管理和组件本地状态的基础关系。
                    删除了复杂的状态同步机制和API相关状态。
                </div>
                <div class="mermaid">
graph TB
    subgraph "全局状态层"
        SlideStore["useSlideStore<br/>页面状态管理<br/>• pages: 页面数组<br/>• activePage: 活动页面索引"]

        VocabStore["useVocabularyStore<br/>词汇状态管理<br/>• modules: 词汇模块数组"]

        SlideStore --> ZustandCore["Zustand Core<br/>DevTools集成"]
        VocabStore --> ZustandCore
    end

    subgraph "组件本地状态层"
        subgraph "SlideView 组件状态"
            SlideLocalState["useState Hook<br/>• selectedGrid: 网格位置对象"]
        end

        subgraph "VocabularyPanel 组件状态"
            VocabLocalState["useState Hook<br/>• texts: 文本记录对象<br/>• editingId: 编辑ID<br/>• editingValue: 编辑值<br/>• collapsedContainers: 折叠容器集合"]
        end

        subgraph "Sidebar 组件状态"
            SidebarLocalState["useState Hook<br/>• editingIndex: number<br/>• editingTitle: string<br/>• isEnterPressed: boolean"]
        end
    end

    subgraph "状态操作层"
        subgraph "SlideStore Actions"
            SlideActions["页面操作方法<br/>• addPage<br/>• deletePage<br/>• updatePageTitle<br/>• setActivePage<br/>• handleCellClick"]
        end

        subgraph "VocabStore Actions"
            VocabActions["词汇操作方法<br/>• addTextItem<br/>• updateTextItem<br/>• deleteTextItem<br/>• toggleModule<br/>• startEditing"]
        end
    end

    %% 状态流向关系
    SlideStore -.->|状态订阅| SlideView["SlideView 组件"]
    SlideStore -.->|状态订阅| Sidebar["Sidebar 组件"]
    VocabStore -.->|状态订阅| VocabPanel["VocabularyPanel 组件"]

    SlideView -.->|状态更新| SlideActions
    Sidebar -.->|状态更新| SlideActions
    VocabPanel -.->|状态更新| VocabActions

    SlideActions -.->|状态变更| SlideStore
    VocabActions -.->|状态变更| VocabStore

    %% 简单的页面切换通信
    SlideStore -.->|页面切换| VocabPanel

    classDef globalState fill:#e8f5e8
    classDef localState fill:#e3f2fd
    classDef actions fill:#fff3e0
    classDef components fill:#fafafa

    class SlideStore,VocabStore,ZustandCore globalState
    class SlideLocalState,VocabLocalState,SidebarLocalState localState
    class SlideActions,VocabActions actions
    class SlideView,Sidebar,VocabPanel components
                </div>

                <div class="features">
                    <h4>状态管理关系图特点</h4>
                    <ul>
                        <li><strong>响应式</strong>: 状态变化自动触发更新</li>
                        <li><strong>类型安全</strong>: 完整的TypeScript类型约束</li>
                        <li><strong>开发友好</strong>: Redux DevTools集成</li>
                    </ul>
                </div>
            </section>

            <!-- 技术栈信息 -->
            <div class="tech-stack">
                <h3>🛠️ 技术栈详情</h3>
                <div class="tech-list">
                    <div class="tech-item">
                        <strong>前端框架</strong>
                        <span>Next.js 14 - React全栈框架</span>
                    </div>
                    <div class="tech-item">
                        <strong>UI库</strong>
                        <span>React 18 - 用户界面库</span>
                    </div>
                    <div class="tech-item">
                        <strong>状态管理</strong>
                        <span>Zustand - 轻量级状态管理</span>
                    </div>
                    <div class="tech-item">
                        <strong>类型系统</strong>
                        <span>TypeScript - 静态类型检查</span>
                    </div>
                    <div class="tech-item">
                        <strong>样式框架</strong>
                        <span>Tailwind CSS - 原子化CSS框架</span>
                    </div>
                    <div class="tech-item">
                        <strong>构建工具</strong>
                        <span>Next.js - 内置构建优化</span>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2025 Cube Frontend 系统架构文档</p>
            <p>文档版本: 1.0.0 | 最后更新: 2025-07-17</p>
        </footer>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
