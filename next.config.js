/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用严格模式
  reactStrictMode: true,
  
  // 图片优化配置
  images: {
    domains: ['localhost'],
    unoptimized: true, // 如果使用静态资源，可以禁用优化
  },
  
  // 静态资源配置
  assetPrefix: process.env.NODE_ENV === 'production' ? '/slide-editor' : '',
  basePath: process.env.NODE_ENV === 'production' ? '/slide-editor' : '',
  
  // API路由配置
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:8080/:path*'
      }
    ]
  },
  
  // CORS头部配置
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ]
  },
  
  // 实验性功能（App Router已默认启用，移除appDir配置）
  experimental: {},
  
  // 构建输出配置
  output: 'standalone', // 或 'export' 用于静态导出
  
  // 环境变量配置
  env: {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
  }
}

module.exports = nextConfig 