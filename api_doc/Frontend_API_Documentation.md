# Cube FastAPI 前端API接口规范文档

## 📋 基础信息

**API基础地址**: `http://localhost:8080`
**请求头**:
```
Content-Type: application/json
Accept: application/json
```

## 🔗 集成说明

本文档描述了前端与Cube FastAPI后端的完整集成规范，包含文本管理、词汇处理、表格管理和坐标管理等功能。

### 核心交互流程：

1. **页面加载时**: 调用 `GET /text/find` 获取所有文本数据
2. **颜色映射**: 按照color字段(0-8)将文本映射到对应颜色的容器
3. **文本编辑**: 调用 `PUT /text/update` 通过ID精确更新文本数据
4. **词汇管理**: 通过 `POST /phrase/add` 和 `DELETE /phrase/delete` 进行智能词汇处理
5. **表格管理**: 通过 `/table/*` 接口管理表格数据（独立于文本系统）
6. **坐标管理**: 通过 `/coordinate/*` 接口处理坐标数据（关联到表格）

### 数据关系说明：

- **TextInfo** ↔ **Phrase**: 一对多关系，Phrase通过text_id关联TextInfo
- **Table** ↔ **Coordinate**: 一对多关系，Coordinate通过table_id关联Table
- **TextInfo与Table**: 无直接关系，是两个独立的业务模块

## 📊 TypeScript类型定义

```typescript
// 文本信息类型
interface TextInfo {
  id: number;
  color: number;  // 0-8，颜色标识
  text: string;   // 文本内容
}

// 词汇类型
interface Phrase {
  id: number;
  text_id: number;  // 关联的TextInfo ID
  word: string;     // 词汇内容
  type: number;     // 词汇类型/重复次数标识
}

// 表格类型
interface Table {
  id: number;
  name: string;
  create_time: string;  // ISO 8601格式，数据库字段名
}

// 坐标类型
interface Coordinate {
  id: number;
  table_id: number;
  color: number;      // 0-8，颜色标识
  position: string;   // 格式："（x， y）"
  voc: string | null; // 词汇内容，可为空
  repeated: number;   // 重复次数，默认0
}

// API响应类型
interface ApiResponse<T = any> {
  message?: string;
  [key: string]: T;
}

// 词汇操作请求类型（用于添加/删除词汇）
interface TextInfoColorUpdate {
  color: number;  // 0-8，颜色标识
  text: string;   // 文本内容
}

// 文本信息更新请求类型（用于直接更新TextInfo）
interface TextInfoUpdate {
  id: number;     // 文本信息ID，必需
  color: number;  // 0-8，颜色标识
  text: string;   // 文本内容
}

// 词汇添加响应类型
interface PhraseAddResponse {
  message: string;
  text_info: TextInfo;  // 始终返回更新后的TextInfo对象
}

// 词汇删除响应类型
interface PhraseDeleteResponse {
  message: string;
  updated_text_infos?: TextInfo[];  // 复杂删除时返回
}
```

## 📝 文本信息管理 API

### 获取所有文本信息
- **接口**: `GET /text/find`
- **方法**: GET
- **参数**: 无
- **功能**: 获取系统中所有的文本信息记录
- **响应**: 返回TextInfo对象数组
```typescript
TextInfo[]
```

**示例响应**:
```json
[
  {
    "id": 1,
    "color": 0,
    "text": "苹果、香蕉、橙子、"
  },
  {
    "id": 2,
    "color": 1,
    "text": "红色、蓝色、绿色、"
  }
]
```

### 更新文本信息
- **接口**: `PUT /text/update`
- **方法**: PUT
- **参数**: 使用`TextInfoUpdate`类型
```typescript
{
  id: number;     // 文本信息ID，用于精确定位记录
  color: number;  // 0-8，颜色标识
  text: string;   // 文本内容
}
```
- **功能**: 根据ID精确更新文本信息
- **说明**: 后端通过 `id` 字段精确定位记录，支持完整的TextInfo对象更新
- **响应**: 返回更新后的完整TextInfo对象
```typescript
TextInfo
```

**示例请求**:
```json
{
  "id": 1,
  "color": 0,
  "text": "苹果、香蕉、橙子、葡萄、"
}
```

**示例响应**:
```json
{
  "id": 1,
  "color": 0,
  "text": "苹果、香蕉、橙子、葡萄、"
}
```

## 🔤 词汇管理 API

### 添加词汇
- **接口**: `POST /phrase/add`
- **方法**: POST
- **参数**: 使用`TextInfoColorUpdate`类型
```typescript
{
  color: number;  // 0-8，颜色标识
  text: string;   // 新的文本内容
}
```
- **功能**: 智能添加词汇，自动处理重复词汇的编号
- **响应**: 始终返回完整的TextInfo对象
```typescript
{
  message: string;
  text_info: TextInfo;  // 更新后的完整TextInfo对象
}
```

**示例请求**:
```json
{
  "color": 0,
  "text": "苹果、香蕉、橙子、苹果、"
}
```

**示例响应（添加成功）**:
```json
{
  "message": "添加成功",
  "text_info": {
    "id": 1,
    "color": 0,
    "text": "苹果、香蕉、橙子、苹果1、"
  }
}
```

**示例响应（无新增）**:
```json
{
  "message": "没有新增词汇",
  "text_info": {
    "id": 1,
    "color": 0,
    "text": "苹果、香蕉、橙子、"
  }
}
```

### 删除词汇 ⭐ 重要更新
- **接口**: `DELETE /phrase/delete`
- **方法**: DELETE
- **参数**: 使用`TextInfoColorUpdate`类型
```typescript
{
  color: number;  // 0-8，颜色标识
  text: string;   // 删除后的文本内容
}
```
- **功能**: 智能删除词汇，支持两种删除模式
- **响应**: 根据删除类型返回不同格式的数据

#### 简单删除（无数字后缀）
删除如"苹果"这样没有数字后缀的词汇时：
```typescript
{
  message: string;
}
```

**示例响应**:
```json
{
  "message": "删除成功"
}
```

#### 复杂删除（有数字后缀）
删除如"苹果1"这样有数字后缀的词汇时，会自动重新编号相关词汇：
```typescript
{
  message: string;
  updated_text_infos: TextInfo[];  // 所有被更新的文本信息
}
```

**示例响应**:
```json
{
  "message": "删除成功",
  "updated_text_infos": [
    {
      "id": 1,
      "color": 0,
      "text": "香蕉、橙子、"
    },
    {
      "id": 2,
      "color": 1,
      "text": "苹果1、葡萄、"
    }
  ]
}
```

**前端处理建议**:
```typescript
// 处理删除响应
const handleDeleteResponse = (response: PhraseDeleteResponse) => {
  if (response.updated_text_infos) {
    // 复杂删除：更新多个文本框
    response.updated_text_infos.forEach(textInfo => {
      updateTextBox(textInfo.color, textInfo.text);
    });
  } else {
    // 简单删除：只需要显示成功消息
    showMessage(response.message);
  }
};
```

## 📊 表格管理 API

### 创建表格
- **接口**: `POST /table/add`
- **方法**: POST
- **参数**: 使用`TableCreate`类型
```typescript
{
  name: string;  // 表格名称，1-255字符
}
```
- **功能**: 创建新表格
- **响应**: 返回创建的表格信息
```typescript
{
  id: number;
  name: string;
  create_time: string;
}
```

**示例请求**:
```json
{
  "name": "我的表格"
}
```

**示例响应**:
```json
{
  "id": 1,
  "name": "我的表格",
  "create_time": "2025-07-24T10:30:00Z"
}
```

### 获取表格列表
- **接口**: `GET /table/page`
- **方法**: GET
- **参数**: 无
- **功能**: 获取所有表格的分页列表
- **响应**:
```typescript
{
  tables: Table[];
  total: number;
}
```

**示例响应**:
```json
{
  "tables": [
    {
      "id": 1,
      "name": "我的表格",
      "create_time": "2025-07-24T10:30:00Z"
    }
  ],
  "total": 1
}
```

### 更新表格
- **接口**: `PUT /table/update`
- **方法**: PUT
- **参数**: 使用`TableUpdate`类型
```typescript
{
  id: number;    // 表格ID，必需且大于0
  name: string;  // 新的表格名称，1-255字符
}
```
- **功能**: 更新指定表格的名称
- **响应**:
```typescript
{
  message: string;
}
```

**示例请求**:
```json
{
  "id": 1,
  "name": "更新后的表格名称"
}
```

**示例响应**:
```json
{
  "message": "更新成功"
}
```

### 删除表格
- **接口**: `DELETE /table/delete?id={id}`
- **方法**: DELETE
- **参数**: `id: number` (查询参数)
- **功能**: 删除指定表格及其关联的坐标数据
- **说明**: 通过数据库级联删除自动删除关联的Coordinate记录
- **响应**:
```typescript
{
  message: string;
}
```

**示例请求**:
```
DELETE /table/delete?id=1
```

**示例响应**:
```json
{
  "message": "删除成功"
}
```

### 根据颜色查询词汇列表
- **接口**: `GET /phrase/list?color={color}`
- **方法**: GET
- **参数**: `color: number` (查询参数，0-8)
- **功能**: 获取指定颜色的所有词汇列表
- **响应**:
```typescript
{
  phrases: Phrase[];
  total: number;
}
```

**示例请求**:
```
GET /phrase/list?color=0
```

**示例响应**:
```json
{
  "phrases": [
    {
      "id": 1,
      "text_id": 1,
      "word": "苹果",
      "type": 0
    },
    {
      "id": 2,
      "text_id": 1,
      "word": "香蕉",
      "type": 0
    }
  ],
  "total": 2
}
```

## 📍 坐标管理 API

### 批量导入坐标数据
- **接口**: `GET /coordinate/batch?id={id}`
- **方法**: GET
- **参数**: `id: number` (表格ID，查询参数)
- **功能**: 从cor.txt文件批量导入坐标数据到指定表格
- **响应**:
```typescript
{
  coordinates: Coordinate[];
  total: number;
}
```

**示例请求**:
```
GET /coordinate/batch?id=1
```

**示例响应**:
```json
{
  "coordinates": [
    {
      "id": 1,
      "table_id": 1,
      "color": 0,
      "position": "（10， 20）",
      "voc": null,
      "repeated": 0
    }
  ],
  "total": 1
}
```

### 删除表格的所有坐标数据
- **接口**: `DELETE /coordinate/delete?id={id}`
- **方法**: DELETE
- **参数**: `id: number` (表格ID，查询参数)
- **功能**: 删除指定表格的所有坐标数据
- **响应**:
```typescript
{
  message: string;
}
```

**示例请求**:
```
DELETE /coordinate/delete?id=1
```

**示例响应**:
```json
{
  "message": "删除成功"
}
```

### 查询表格的坐标数据
- **接口**: `GET /coordinate/find?id={id}`
- **方法**: GET
- **参数**: `id: number` (表格ID，查询参数)
- **功能**: 获取指定表格的所有坐标数据
- **响应**:
```typescript
{
  coordinates: Coordinate[];
  total: number;
}
```

**示例请求**:
```
GET /coordinate/find?id=1
```

**示例响应**:
```json
{
  "coordinates": [
    {
      "id": 1,
      "table_id": 1,
      "color": 0,
      "position": "（10， 20）",
      "voc": "苹果",
      "repeated": 1
    }
  ],
  "total": 1
}
```

### 查询坐标关联的词汇列表
- **接口**: `GET /coordinate/list?color={color}&table_id={table_id}&coordinate_id={coordinate_id}`
- **方法**: GET
- **参数**:
  - `color: number` (颜色标识，查询参数)
  - `table_id: number` (表格ID，查询参数)
  - `coordinate_id: number` (坐标ID，查询参数)
- **功能**: 获取指定坐标关联的词汇列表
- **响应**:
```typescript
{
  phrases: Phrase[];
  total: number;
}
```

**示例请求**:
```
GET /coordinate/list?color=0&table_id=1&coordinate_id=1
```

**示例响应**:
```json
{
  "phrases": [
    {
      "id": 1,
      "text_id": 1,
      "word": "苹果",
      "type": 0
    }
  ],
  "total": 1
}
```

### 更新坐标信息
- **接口**: `PUT /coordinate/update`
- **方法**: PUT
- **参数**: 使用`CoordinateUpdate`类型
```typescript
{
  id: number;           // 坐标ID
  table_id: number;     // 表格ID
  color: number;        // 颜色标识，0-8
  position: string;     // 坐标位置，格式："（x， y）"
  voc?: string;         // 词汇内容，可选，默认为空字符串
  repeated?: number;    // 重复次数，可选，默认为0
}
```
- **功能**: 更新指定坐标的信息
- **响应**:
```typescript
{
  coordinates: Coordinate[];
}
```

**示例请求**:
```json
{
  "id": 1,
  "table_id": 1,
  "color": 0,
  "position": "（15， 25）",
  "voc": "苹果",
  "repeated": 2
}
```

**示例响应**:
```json
{
  "coordinates": [
    {
      "id": 1,
      "table_id": 1,
      "color": 0,
      "position": "（15， 25）",
      "voc": "苹果",
      "repeated": 2
    }
  ]
}
```

## 💻 前端请求示例

### 基础请求函数
```typescript
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`http://localhost:8080${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};
```

### 使用示例

#### 获取文本信息列表
```typescript
const getTextInfoList = async (): Promise<TextInfo[]> => {
  return await apiRequest('/text/find') as TextInfo[];
};
```

#### 更新文本信息（通过ID精确更新）
```typescript
const updateTextInfo = async (id: number, color: number, text: string): Promise<TextInfo> => {
  const response = await apiRequest('/text/update', {
    method: 'PUT',
    body: JSON.stringify({ id, color, text }),
  }) as TextInfo;

  // 直接使用返回的TextInfo对象更新前端
  updateTextBoxById(response.id, response.text);

  console.log('更新成功:', response);
  return response;
};

// 简化版本：直接传入TextInfoUpdate对象
const updateTextInfoByObject = async (textInfo: TextInfoUpdate): Promise<TextInfo> => {
  return await apiRequest('/text/update', {
    method: 'PUT',
    body: JSON.stringify(textInfo),
  }) as TextInfo;
};
```

#### 添加词汇（返回完整TextInfo对象）
```typescript
const addPhrase = async (color: number, text: string): Promise<PhraseAddResponse> => {
  const response = await apiRequest('/phrase/add', {
    method: 'POST',
    body: JSON.stringify({ color, text }),
  }) as PhraseAddResponse;

  // 直接使用返回的TextInfo对象更新前端
  const textInfo = response.text_info;
  updateTextBoxById(textInfo.id, textInfo.text);

  console.log(response.message, '更新的TextInfo:', textInfo);
  return response;
};

// 辅助函数：通过ID更新文本框
const updateTextBoxById = (id: number, text: string) => {
  const textBox = document.querySelector(`[data-text-id="${id}"]`);
  if (textBox) {
    textBox.textContent = text;
  }
};
```

#### 删除词汇（重要：支持两种返回格式）
```typescript
const deletePhrase = async (color: number, text: string): Promise<PhraseDeleteResponse> => {
  const response = await apiRequest('/phrase/delete', {
    method: 'DELETE',
    body: JSON.stringify({ color, text }),
  }) as PhraseDeleteResponse;

  // 处理不同的返回格式
  if (response.updated_text_infos) {
    // 复杂删除：更新多个文本框
    console.log('复杂删除，需要更新多个文本框:', response.updated_text_infos);
    response.updated_text_infos.forEach(textInfo => {
      updateTextBox(textInfo.color, textInfo.text);
    });
  } else {
    // 简单删除：只显示成功消息
    console.log('简单删除成功:', response.message);
  }

  return response;
};

// 辅助函数：更新文本框
const updateTextBox = (color: number, text: string) => {
  const textBox = document.querySelector(`[data-color="${color}"]`);
  if (textBox) {
    textBox.textContent = text;
  }
};
```

#### 创建表格
```typescript
const createTable = async (name: string): Promise<Table> => {
  return await apiRequest('/table/add', {
    method: 'POST',
    body: JSON.stringify({ name }),
  }) as Table;
};
```

#### 删除表格
```typescript
const deleteTable = async (id: number) => {
  return await apiRequest(`/table/delete?id=${id}`, {
    method: 'DELETE',
  });
};
```

#### 批量导入坐标
```typescript
const importCoordinates = async (tableId: number) => {
  return await apiRequest(`/coordinate/batch?id=${tableId}`);
};
```

## ⚠️ 错误处理

### 统一错误响应格式
```typescript
interface ApiError {
  detail: string;
  status_code?: number;
}
```

### 错误处理示例
```typescript
try {
  const data = await apiRequest('/text/find');
  console.log(data);
} catch (error) {
  console.error('请求失败:', error.message);
  // 根据错误类型进行不同处理
  if (error.message.includes('404')) {
    console.log('资源不存在');
  } else if (error.message.includes('400')) {
    console.log('请求参数错误');
  }
}
```

## 🔄 最佳实践

### 词汇添加的前端处理流程
```typescript
const handlePhraseAdd = async (color: number, text: string) => {
  try {
    const response = await addPhrase(color, text);

    // 使用返回的TextInfo对象直接更新界面
    const textInfo = response.text_info;
    updateTextBoxWithAnimation(textInfo.id, textInfo.text);

    // 更新本地状态
    updateTextBoxState(textInfo);

    console.log(`添加完成: ${response.message}`);
  } catch (error) {
    console.error('添加失败:', error);
    showErrorMessage('添加失败，请重试');
  }
};
```

### 词汇删除的前端处理流程
```typescript
const handlePhraseDelete = async (color: number, newText: string) => {
  try {
    const response = await deletePhrase(color, newText);

    if (response.updated_text_infos) {
      // 复杂删除：批量更新界面
      console.log(`复杂删除完成，更新了 ${response.updated_text_infos.length} 个文本框`);

      // 可以添加动画效果
      response.updated_text_infos.forEach((textInfo, index) => {
        setTimeout(() => {
          updateTextBoxWithAnimation(textInfo.color, textInfo.text);
        }, index * 100); // 错开动画时间
      });
    } else {
      // 简单删除：只更新当前文本框
      console.log('简单删除完成');
      updateTextBox(color, newText);
    }
  } catch (error) {
    console.error('删除失败:', error);
    // 显示错误提示
    showErrorMessage('删除失败，请重试');
  }
};
```

### 文本框状态管理
```typescript
// 建议使用状态管理来跟踪文本框的变化
interface TextBoxState {
  [id: number]: {
    id: number;
    color: number;
    text: string;
    isEditing: boolean;
    lastUpdated: Date;
  };
}

const textBoxState: TextBoxState = {};

// 更新状态的函数（基于ID）
const updateTextBoxState = (textInfo: TextInfo) => {
  textBoxState[textInfo.id] = {
    id: textInfo.id,
    color: textInfo.color,
    text: textInfo.text,
    isEditing: false,
    lastUpdated: new Date()
  };
};

// 通过颜色查找TextInfo
const findTextInfoByColor = (color: number): TextInfo | undefined => {
  return Object.values(textBoxState).find(state => state.color === color);
};

// 批量更新状态
const updateMultipleTextBoxStates = (textInfos: TextInfo[]) => {
  textInfos.forEach(textInfo => {
    updateTextBoxState(textInfo);
  });
};
```

---

## 📋 文档信息

**文档版本**: 2.1.0
**最后更新**: 2025-07-24
**维护团队**: Cube FastAPI 开发组

### 更新日志
- **v2.1.0**: 修正数据关系描述，删除不存在的业务逻辑，确保文档与实际代码完全一致
- **v2.0.0**: 全面审查和更新API文档，确保与实际代码完全一致，删除冗余信息
- **v1.5.0**: TextInfo更新接口支持ID精确更新，返回完整TextInfo对象
- **v1.4.0**: 词汇添加接口返回完整TextInfo对象，支持前端精确更新
- **v1.3.0**: 添加词汇删除的差异化返回结果支持
- **v1.2.0**: 完善所有API接口文档和示例
- **v1.1.0**: 添加TypeScript类型定义
- **v1.0.0**: 初始版本
